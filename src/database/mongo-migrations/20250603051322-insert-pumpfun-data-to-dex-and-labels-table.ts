import { Db } from 'mongodb';
import { DexName } from 'src/common/constant/solana';

export = {
  async up(db: Db): Promise<void> {
    const now = new Date();

    // Insert PumpFun DEX data
    const dexData = {
      name: 'Pumpfun',
      createdAt: now,
      updatedAt: now,
    };

    const insertedDex = await db.collection('dex').insertOne(dexData);
    const dexId = insertedDex.insertedId.toString();

    // Insert DEX labels
    const dexLabels = [
      {
        programAddress: '6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P',
        dexId,
        label: DexName.PUMPFUN,
        description: 'Pumpfun',
        createdAt: now,
        updatedAt: now,
      },
      {
        programAddress: 'pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA',
        dexId,
        label: DexName.PUMPSWAP,
        description: 'Pumpswap',
        createdAt: now,
        updatedAt: now,
      },
    ];

    await db.collection('dex_label').insertMany(dexLabels);
  },

  async down(db: Db): Promise<void> {
    // Remove PumpFun DEX data
    const dex = await db.collection('dex').findOne({ name: 'Pumpfun' });
    if (dex) {
      const dexId = dex._id.toString();

      // Remove dex labels first (foreign key relationship)
      await db.collection('dex_label').deleteMany({ dexId });

      // Then remove the dex
      await db.collection('dex').deleteOne({ _id: dex._id });
    }
  },
};
