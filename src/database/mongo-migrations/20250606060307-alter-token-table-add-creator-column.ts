import { Db } from 'mongodb';

export = {
  async up(db: Db): Promise<void> {
    // Modify the schema to add the 'creator' field
    await db.command({
      collMod: 'token',
      validator: {
        $jsonSchema: {
          bsonType: 'object',
          required: ['tokenAddress', 'creator'],
          properties: {
            tokenAddress: { bsonType: 'string' },
            tokenName: { bsonType: 'string' },
            symbol: { bsonType: 'string' },
            description: { bsonType: 'string' },
            image: { bsonType: 'string' },
            bannerImage: { bsonType: 'string' },
            decimals: { bsonType: 'number' },
            twitter: { bsonType: 'string' },
            website: { bsonType: 'string' },
            telegram: { bsonType: 'string' },
            createdAt: { bsonType: 'date' },
            updatedAt: { bsonType: 'date' },
            creator: {
              bsonType: 'string',
              description: 'Creator wallet address (required)',
            },
          },
        },
      },
      validationLevel: 'moderate',
    });

    // Optional: add index for `creator` if it's queried often
    await db
      .collection('token')
      .createIndex({ creator: 1 }, { background: true });
  },

  async down(db: Db): Promise<void> {
    // Drop the creator index
    await db.collection('token').dropIndex('creator_1');

    // Revert schema by removing the 'creator' field
    await db.command({
      collMod: 'token',
      validator: {
        $jsonSchema: {
          bsonType: 'object',
          required: ['tokenAddress'],
          properties: {
            tokenAddress: { bsonType: 'string' },
            tokenName: { bsonType: 'string' },
            symbol: { bsonType: 'string' },
            description: { bsonType: 'string' },
            image: { bsonType: 'string' },
            bannerImage: { bsonType: 'string' },
            decimals: { bsonType: 'number' },
            twitter: { bsonType: 'string' },
            website: { bsonType: 'string' },
            telegram: { bsonType: 'string' },
            createdAt: { bsonType: 'date' },
            updatedAt: { bsonType: 'date' },
          },
        },
      },
      validationLevel: 'moderate',
    });
  },
};
