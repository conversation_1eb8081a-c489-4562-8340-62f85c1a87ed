import { TxnsCount } from '../../../swap-transaction/dto/txns-count.dto';
import { VolumeResult } from '../../../swap-transaction/dto/volume-result.dto';

export class PairPumpSwapDetailDto {
  pairAddress: string;
  marketCapUsd: number;
  liquidityUsd: number;
  priceUsd: number;
  pooledSOL: number;
  totalTxns: TxnsCount;
  totalVolume: VolumeResult;
  numOfBuyers: number;
  numOfSellers: number;
  volume24h: number;
  createdAt: Date;
  updatedAt: Date;
}
