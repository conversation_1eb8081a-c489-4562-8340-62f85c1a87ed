import { PumpSwapPairType } from '../../../common/constant/pumpswap/pumpswap';

export type CreatePairDto = {
  signatureHash: string;
  pairAddress: string;
  creator: string;
  baseAddress: string;
  quoteAddress: string;
  baseVault: string;
  quoteVault: string;
  dexLabel: string;
  blockTime: Date;
  extra: PumpfunExtra | PumpSwapExtras | null;
};

export type PumpfunExtra = {
  associatedBondingCurve: string;
};

export type PumpSwapExtras = {
  pairType: PumpSwapPairType;
};
