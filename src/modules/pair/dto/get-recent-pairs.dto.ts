import { IsInt, IsOptional, IsIn, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';
import { DexName } from 'src/common/constant/solana';

export const sortableFields = ['liquidity_usd', 'market_cap_usd'];
export const filterableFields = Object.values(DexName);

export class GetRecentPairsDto {
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  @IsOptional()
  limit: number = 50;

  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsOptional()
  page: number = 1;

  @IsOptional()
  @IsIn(filterableFields, {
    message: `filterBy must be one of: ${filterableFields.join(', ')}`,
  })
  filterBy?: DexName;

  @IsOptional()
  @IsIn(sortableFields, {
    message: `sortBy must be one of: ${sortableFields.join(', ')}`,
  })
  sortBy?: string;

  @IsOptional()
  @IsIn(['asc', 'desc'])
  sortDirection: 'asc' | 'desc' = 'desc';
}
