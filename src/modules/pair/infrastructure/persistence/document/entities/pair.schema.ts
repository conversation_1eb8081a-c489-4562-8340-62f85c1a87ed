import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { now, Document, SchemaTypes } from 'mongoose';

export type PairSchemaDocument = PairSchemaClass & Document;
@Schema({
  collection: 'pair',
})
export class PairSchemaClass {
  @Prop({ required: true })
  signatureHash: string;

  @Prop({ required: true })
  pairAddress: string;

  @Prop({ required: false })
  associatedBondingCurve: string;

  @Prop({ required: true })
  baseAddress: string;

  @Prop({ required: true })
  quoteAddress: string;

  @Prop({ required: true })
  creator: string;

  @Prop({ required: false })
  dexLabel: string;

  @Prop({ default: now })
  createdAt?: Date;

  @Prop({ default: now })
  updatedAt?: Date;

  @Prop({ type: SchemaTypes.Mixed, required: false })
  extra?: Record<string, any>;
}

export const PairSchema = SchemaFactory.createForClass(PairSchemaClass);
