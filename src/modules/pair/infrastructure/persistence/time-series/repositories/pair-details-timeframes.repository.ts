import { Injectable, Logger } from '@nestjs/common';
import { QuestdbPgService } from 'src/modules/questdb/questdb-pg.service';
import { QuestdbSenderService } from 'src/modules/questdb/questdb-sender.service';
import { PairDetailTimeframes } from 'src/modules/pair/domain/pair-detail-timeframes';
import { QuestDbTable } from 'src/common/constant/questdb.constants';
import {
  buildCheckPairDetailTimeFrameExistsQuery,
  buildGetPairDetailTimeframesByAddressAndTimeIntervalQuery,
  buildUpdatePairDetailTimeframesQuery,
} from 'src/modules/questdb/questdb.query-builder';
import { PairDetailTimeframesMapper } from '../mappers/pair-detail-timeframes.mapper';
import { PairDetailTimeframesEntity } from '../entities/pair-detail-timeframes.entity';

@Injectable()
export class PairDetailTimeframesRepository {
  private readonly logger: Logger = new Logger(
    PairDetailTimeframesRepository.name,
  );

  constructor(
    private readonly questdbPgService: QuestdbPgService,
    private readonly questdbSenderService: QuestdbSenderService,
  ) {}

  async createPairDetailTimeframes(pairDetailTimeframes: PairDetailTimeframes) {
    const begin = performance.now();

    try {
      await this.questdbSenderService.queuePairDetailTimeframes({
        pairAddress: pairDetailTimeframes.pairAddress,
        timeInterval: pairDetailTimeframes.timeInterval,
        buyVolumeUsd: pairDetailTimeframes.buyVolumeUsd,
        sellVolumeUsd: pairDetailTimeframes.sellVolumeUsd,
        numOfBuyTxs: pairDetailTimeframes.numOfBuyTxs,
        numOfSellTxs: pairDetailTimeframes.numOfSellTxs,
        numOfBuyers: pairDetailTimeframes.numOfBuyers,
        numOfSellers: pairDetailTimeframes.numOfSellers,
        updatedAt: pairDetailTimeframes.updatedAt,
        ts: new Date(),
      });

      this.logger.log(
        `[SUCCESS] Completed storing pair detail timeframes for [${pairDetailTimeframes.pairAddress}], elapsed: ${(performance.now() - begin).toFixed()} ms`,
      );

      return { status: 'inserted' };
    } catch (error) {
      this.logger.error(
        `[FAILURE] Failed to store pair detail timeframes for [${pairDetailTimeframes.pairAddress}]: ${error.message}`,
        error.stack,
      );
      return { status: 'failed', reason: error.message };
    }
  }

  async updatePairDetailTimeframes(
    updatedPairDetailTimeframes: PairDetailTimeframes,
  ) {
    const nowMs = Date.now();

    if (!updatedPairDetailTimeframes?.pairAddress) {
      this.logger.verbose('Invalid pair detail or missing pair address');
      return;
    }

    const ts = updatedPairDetailTimeframes.ts;
    if (nowMs - ts.getTime() <= 2000) return;

    const checkSql = buildCheckPairDetailTimeFrameExistsQuery(
      updatedPairDetailTimeframes.pairAddress,
    );
    const exists = await this.questdbPgService.query(checkSql);

    if (exists && exists.length > 0) {
      const sql = buildUpdatePairDetailTimeframesQuery(
        updatedPairDetailTimeframes,
      );
      try {
        await this.questdbPgService.query(sql);
        this.logger.log(
          `[SUCCESS] Updated pair detail timeframes for [${updatedPairDetailTimeframes.pairAddress}] via UPDATE`,
        );
      } catch (err) {
        this.logger.error(
          `Failed to update pair detail timeframes for [${updatedPairDetailTimeframes.pairAddress}]: ${err.message}`,
        );
      }
    } else {
      this.logger.warn(
        `Pair address [${updatedPairDetailTimeframes.pairAddress}] does not exist in ${QuestDbTable.PAIR_DETAIL_TIMEFRAMES}`,
      );
    }
  }

  async findPairDetailsTimeframesByPairAddress(
    pairAddress: string,
    interval: string,
  ): Promise<PairDetailTimeframes> {
    if (!pairAddress) return;

    try {
      const sql = buildGetPairDetailTimeframesByAddressAndTimeIntervalQuery(
        pairAddress,
        interval,
      );
      const rawResult =
        await this.questdbPgService.query<PairDetailTimeframesEntity[]>(sql);

      if (!rawResult?.length) return;

      const pd = rawResult[0];

      return PairDetailTimeframesMapper.toDomain(pd);
    } catch (error) {
      this.logger.error(
        `Failed to fetch pair detail for address [${pairAddress}]:`,
        error,
      );
      return;
    }
  }
}
