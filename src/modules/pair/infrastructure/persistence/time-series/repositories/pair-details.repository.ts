import { Injectable, Logger } from '@nestjs/common';
import { QuestdbPgService } from 'src/modules/questdb/questdb-pg.service';
import { QuestdbSenderService } from 'src/modules/questdb/questdb-sender.service';
import {
  buildCheckPairExistsQuery,
  buildGetPairDetailByAddressQuery,
  buildLatestPairDetailsQuery,
  buildUpdatePairDetailQuery,
} from 'src/modules/questdb/questdb.query-builder';
import { QuestDbTable } from 'src/common/constant/questdb.constants';
import { SortDirection } from '../../../../../../common/constant/sort-direction';
import { PairDetailEntity } from '../entities/pair-detail.entity';
import { PairDetailMapper } from '../mappers/pair-detail.mapper';
import { PairDetail } from '../../../../domain/pair-detail';
import { CreatePairDetailResult } from '../../../../dto/pair-detail/create-pair-detail-result.dto';

@Injectable()
export class PairDetailRepository {
  private readonly logger: Logger = new Logger(PairDetailRepository.name);

  constructor(
    private readonly questdbPgService: QuestdbPgService,
    private readonly questdbSenderService: QuestdbSenderService,
  ) {}

  async createPairDetail(
    pairDetail: PairDetail,
  ): Promise<CreatePairDetailResult> {
    const begin = performance.now();

    try {
      await this.questdbSenderService.createPairDetail({
        pairAddress: pairDetail.pairAddress,
        marketCapUsd: pairDetail.marketCapUsd,
        liquidityUsd: pairDetail.liquidityUsd,
        buyVolumeUsd: pairDetail.buyVolumeUsd,
        sellVolumeUsd: pairDetail.sellVolumeUsd,
        numOfBuyTxs: pairDetail.numOfBuyTxs,
        numOfSellTxs: pairDetail.numOfSellTxs,
        process: pairDetail.process,
        numOfBuyers: pairDetail.numOfBuyers,
        numOfSellers: pairDetail.numOfSellers,
        createdAt: pairDetail.createdAt,
        updatedAt: pairDetail.updatedAt,
        priceUsd: pairDetail.priceUsd,
        pooledSol: pairDetail.pooledSol,
        ts: pairDetail.ts,
      });

      await this.questdbSenderService.flush();

      this.logger.log(
        `[SUCCESS] Completed storing pair detail for [${pairDetail.pairAddress}], elapsed: ${(performance.now() - begin).toFixed()} ms`,
      );

      return { status: 'inserted' };
    } catch (error) {
      this.logger.error(
        `[FAILURE] Failed to store pair detail for [${pairDetail.pairAddress}]: ${error.message}`,
        error.stack,
      );
      return { status: 'failed', reason: error.message };
    }
  }

  async findOneByPairAddress(
    pairAddress: string,
  ): Promise<PairDetail | undefined> {
    if (!pairAddress) return;

    try {
      const sql = buildGetPairDetailByAddressQuery(pairAddress);
      const rawResult =
        await this.questdbPgService.query<PairDetailEntity[]>(sql);

      if (!rawResult?.length) return;

      const pd = rawResult[0];

      return PairDetailMapper.toDomain(pd);
    } catch (error) {
      this.logger.error(
        `Failed to fetch pair detail for address [${pairAddress}]:`,
        error,
      );
      return;
    }
  }

  async updatePairDetailPumpFun(pair: PairDetail) {
    if (!pair?.pairAddress) {
      this.logger.verbose('Invalid pair detail or missing pair address');
      return;
    }

    const checkSql = buildCheckPairExistsQuery(pair.pairAddress);
    const exists = await this.questdbPgService.query(checkSql);

    if (exists && exists.length > 0) {
      const sql = buildUpdatePairDetailQuery(pair);

      try {
        await this.questdbPgService.query(sql);
        this.logger.log(
          `[SUCCESS] Updated pair detail for [${pair.pairAddress}] via UPDATE`,
        );
      } catch (err) {
        this.logger.error(
          `Failed to update pair detail for [${pair.pairAddress}]: ${err.message}`,
        );
      }
    } else {
      this.logger.warn(
        `Pair address [${pair.pairAddress}] does not exist in ${QuestDbTable.PAIR_DETAIL}`,
      );
    }
  }

  async updatePairDetailPumpSwap(pair: PairDetail) {
    const sql = buildUpdatePairDetailQuery(pair);
    try {
      await this.questdbPgService.query(sql);
      this.logger.log(
        `[SUCCESS] Updated pair detail for [${pair.pairAddress}] via UPDATE`,
      );
    } catch (err) {
      this.logger.error(
        `Failed to update pair detail for [${pair.pairAddress}]: ${err.message}`,
      );
    }
  }

  async findLastestPairDetails(
    pairAddresses: string[],
    sortField: string,
    direction: SortDirection,
  ) {
    const query = buildLatestPairDetailsQuery(
      pairAddresses,
      sortField,
      direction,
    );
    const pairDetails =
      await this.questdbPgService.query<PairDetailEntity[]>(query);
    return pairDetails.map(PairDetailMapper.toDomain);
  }
}
