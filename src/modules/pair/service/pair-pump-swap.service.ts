import { Inject, Injectable, Logger } from '@nestjs/common';
import _, { flatten } from 'lodash';
import { InjectFlowProducer, InjectQueue } from '@nestjs/bullmq';
import { PumpSwapQueue } from '../../../common/constant/queue-name';
import { FlowProducer, Queue } from 'bullmq';
import {
  PUMP_SWAP_NEW_PAIR_DETAIL_JOB,
  PUMP_SWAP_NEW_PAIR_TRANSACTION_JOB,
  PUMP_SWAP_UPDATE_PAIR_DETAIL_JOB,
} from '../../../common/constant/job-name';
import {
  PUMPFUN_MIGRATION_SIGNER,
  PUMPFUN_PROGRAM_ID,
} from '../../../common/constant/pumpfun/pumpfun';
import {
  PUMPSWAP_AND_SOL_DIFFERENTIAL,
  PUMPSWAP_PROGRAM_ID,
  PumpSwapPairType,
} from 'src/common/constant/pumpswap/pumpswap';
import { TOKEN_WSOL } from '../../token/dto/token-wsol';
import { PairDocumentRepository } from '../infrastructure/persistence/document/repositories/pair-document.repository';
import { PairSchemaClass } from '../infrastructure/persistence/document/entities/pair.schema';
import { CreatePairDto, PumpSwapExtras } from '../dto/create-pair.dto';
import { CreatePumpSwapPairDetailDto } from '../dto/pair-detail/create-pump-swap-pair-detail.dto';
import { DexLabelService } from '../../dex-label/dex-label.service';
import { TransactionData } from '../../../common/type/transaction.data';
import {
  InnerInstruction,
  Instruction,
} from '../../../common/type/instruction';
import { UpdatePumpSwapPairDetailDto } from '../../swap-transaction/dto/update-pump-swap-pair-detail.dto';
import { Connection, LAMPORTS_PER_SOL, PublicKey } from '@solana/web3.js';
import { PairPumpSwapDetailDto } from '../dto/pair-detail/pair-detail.dto';
import BigNumber from 'bignumber.js';
import { PairDetail } from '../domain/pair-detail';
import { PairDetailRepository } from '../infrastructure/persistence/time-series/repositories/pair-details.repository';
import { SwapTransactionPumpSwapService } from '../../swap-transaction/services/swap-transaction-pump-swap.service';
import { SolanaService } from '../../solana/solana.service';
import { RedisRepository } from '../../redis/redis.repository';
import { PairService } from './pair.service';
import { PairDetailTimeframesRepository } from '../infrastructure/persistence/time-series/repositories/pair-details-timeframes.repository';

@Injectable()
export class PairPumpSwapService {
  private readonly logger = new Logger(PairPumpSwapService.name);
  constructor(
    @InjectFlowProducer(PumpSwapQueue.NEW_PUMPSWAP_PAIR)
    private readonly newPairQueue: FlowProducer,
    @Inject()
    private readonly pairDocumentRepository: PairDocumentRepository,
    @Inject()
    private readonly pairDetailRepository: PairDetailRepository,
    @Inject()
    private readonly pairDetailTimeFramesRepository: PairDetailTimeframesRepository,
    @Inject()
    private readonly pairService: PairService,
    @Inject()
    private readonly redisRepository: RedisRepository,
    @Inject()
    private readonly dexLabelService: DexLabelService,
    @Inject(SwapTransactionPumpSwapService)
    private readonly swapTransactionPumpSwapService: SwapTransactionPumpSwapService,
    @Inject(SolanaService) private readonly solanaService: SolanaService,
    @Inject('SolanaConnection') private readonly connection: Connection,
    @InjectQueue(PumpSwapQueue.UPDATE_MULTIPLE_PAIR)
    private readonly updateMultiplePoolQueue: Queue,
  ) {}

  async handlePumpSwapPair(
    parsedTransactionData: TransactionData,
    blockTime: number,
  ) {
    const { instructions, logs, signature, innerInstructions } =
      parsedTransactionData;

    const isFoundCreatedPool = logs?.some((log: string | string[]) => {
      return log.includes('Instruction: CreatePool');
    });

    const isMigrateFromPumpFun = logs?.some((log: string | string[]) => {
      return log.includes('Instruction: Migrate');
    });

    if (!isMigrateFromPumpFun) {
      return;
    }

    if (!isFoundCreatedPool) {
      return;
    }

    const isSigner = parsedTransactionData.accountKeys.some(
      (account) => account.pubkey === PUMPFUN_MIGRATION_SIGNER,
    );
    const signer = isSigner ? 'OFFICIAL' : 'CUSTOM';

    const migratePoolPumpFunInstruction = instructions.filter(
      (instruction: Instruction) =>
        instruction.programId === PUMPFUN_PROGRAM_ID &&
        instruction.accounts.length >= 24,
    );

    const innerIns: Instruction[] = flatten(
      innerInstructions.map((instruction) => instruction.instructions),
    );

    const migratePoolPumpFunInnerInstruction = innerIns.filter(
      (innerInstruction) =>
        innerInstruction.programId === PUMPFUN_PROGRAM_ID &&
        innerInstruction.accounts.length >= 24,
    );

    const createPoolPumpSwapInnerInstructions = innerIns.filter(
      (instruction) =>
        instruction.programId === PUMPSWAP_PROGRAM_ID &&
        instruction.accounts.length === 18,
    );

    let pairType: PumpSwapPairType = PumpSwapPairType.PUMPFUN_MIGRATION;

    if (
      migratePoolPumpFunInstruction.length > 0 &&
      createPoolPumpSwapInnerInstructions.length > 0 &&
      signer === 'OFFICIAL'
    ) {
      pairType = PumpSwapPairType.PUMPFUN_MIGRATION;
    } else if (
      migratePoolPumpFunInstruction.length > 0 &&
      createPoolPumpSwapInnerInstructions.length > 0 &&
      signer === 'CUSTOM'
    ) {
      pairType = PumpSwapPairType.CUSTOM;
    } else if (
      migratePoolPumpFunInnerInstruction.length > 0 &&
      createPoolPumpSwapInnerInstructions.length > 0 &&
      signer === 'CUSTOM'
    ) {
      pairType = PumpSwapPairType.CUSTOM_PUMPFUN_MIGRATION;
    }

    const createPoolInstructions = instructions.filter(
      (instruction: Instruction) =>
        instruction.programId === PUMPFUN_PROGRAM_ID,
    );
    for (const createPoolInstruction of createPoolInstructions) {
      const createPairRequest: CreatePairDto =
        this.populateToCreatePumpSwapPairRequest(
          createPoolInstruction,
          innerInstructions,
          signature,
        );
      if (createPairRequest === null) {
        continue;
      }
      this.logger.log(
        `Found new PumpSwap [${createPairRequest.pairAddress}], transaction [${signature}]`,
      );
      createPairRequest.extra = { pairType: pairType } as PumpSwapExtras;
      createPairRequest.blockTime = new Date(blockTime);
      await this.addNewPairToQueue(createPairRequest);
    }

    for (const createPoolInstruction of createPoolPumpSwapInnerInstructions) {
      const createPairRequest: CreatePairDto =
        this.populateToCreatePumpSwapPairRequest(
          createPoolInstruction,
          innerInstructions,
          signature,
        );
      if (createPairRequest === null) {
        continue;
      }
      createPairRequest.extra = { pairType: pairType } as PumpSwapExtras;
      createPairRequest.blockTime = new Date(blockTime);
      this.logger.log(
        `Found new migrated PumpSwap [${createPairRequest.pairAddress}], transaction [${signature}]`,
      );
      await this.addNewPairToQueue(createPairRequest);
    }
  }

  async handleCreatePumpSwapPair(createPairRequest: CreatePairDto) {
    try {
      const beginHandle = new Date();
      const baseAddress = createPairRequest.baseAddress;
      const quoteAddress = createPairRequest.quoteAddress;
      if (baseAddress === TOKEN_WSOL.address) {
        createPairRequest.quoteAddress = baseAddress;
        createPairRequest.baseAddress = quoteAddress;
      }
      const dexLabel =
        await this.dexLabelService.findByProgramAddress(PUMPSWAP_PROGRAM_ID);
      createPairRequest.dexLabel = dexLabel.label;
      await this.createPumpSwapPair(createPairRequest);
      this.logger.verbose(
        `Completed to create pair [${createPairRequest.pairAddress}], [${new Date().getTime() - beginHandle.getTime()}] ms`,
      );
      return this.populatePairPumpSwapDtoToCreatePairPumpSwapDetailDto(
        createPairRequest,
      );
    } catch (err) {
      this.logger.error(
        `Failed to create pair [${createPairRequest.pairAddress}] with reason: ${err.message}`,
      );
      this.logger.debug(err.stack);
      throw new Error(err);
    }
  }

  private populateToCreatePumpSwapPairRequest(
    instruction: Instruction,
    innerInstructions: InnerInstruction[],
    signature: string,
  ) {
    const accounts = instruction.accounts;
    const pairAddress = accounts[0];
    const _lqMint = accounts[5];
    let baseMint = accounts[3];
    let quoteMint = accounts[4];
    const baseVault = accounts[9];
    const quoteVault = accounts[10];
    const creator = accounts[2];

    if (quoteMint !== TOKEN_WSOL.address && baseMint !== TOKEN_WSOL.address) {
      return null;
    }

    if (baseMint === TOKEN_WSOL.address) {
      baseMint = TOKEN_WSOL.wsol;
    } else {
      quoteMint = TOKEN_WSOL.wsol;
    }

    const _decimals = _.chain(innerInstructions)
      .flatMap('instructions') // Flatten all instructions
      .filter((instr) => _.get(instr, 'parsed.type') === 'initializeMint2') // Filter by type
      .map('parsed.info.decimals') // Extract decimals
      .first() // Get the first value
      .value(); // Resolve the Lodash chain
    return {
      signatureHash: signature,
      pairAddress: pairAddress,
      creator: creator,
      baseAddress: baseMint,
      quoteAddress: quoteMint,
      baseVault: baseVault,
      quoteVault: quoteVault,
      blockTime: new Date(),
    } as CreatePairDto;
  }

  private async addNewPairToQueue(createPairRequest: CreatePairDto) {
    const pairDocument = await this.pairDocumentRepository.findByPairAddress(
      createPairRequest.pairAddress,
    );
    if (pairDocument) {
      return;
    }
    await this.newPairQueue.add({
      name: PUMP_SWAP_NEW_PAIR_DETAIL_JOB,
      queueName: PumpSwapQueue.NEW_PAIR_DETAIL,
      children: [
        {
          name: PUMP_SWAP_NEW_PAIR_TRANSACTION_JOB,
          queueName: PumpSwapQueue.NEW_PUMPSWAP_PAIR,
          data: createPairRequest,
          opts: {
            failParentOnFailure: true,
          },
        },
      ],
    });
  }

  private populatePairPumpSwapDtoToCreatePairPumpSwapDetailDto(
    pairPumpSwapDto: CreatePairDto,
  ): CreatePumpSwapPairDetailDto {
    const createPairPumpSwapDetailDto = new CreatePumpSwapPairDetailDto();
    createPairPumpSwapDetailDto.baseAddress = pairPumpSwapDto.baseAddress;
    createPairPumpSwapDetailDto.quoteAddress = pairPumpSwapDto.quoteAddress;
    createPairPumpSwapDetailDto.signatureHash = pairPumpSwapDto.signatureHash;
    createPairPumpSwapDetailDto.pairAddress = pairPumpSwapDto.pairAddress;
    createPairPumpSwapDetailDto.pairType = (
      pairPumpSwapDto.extra as PumpSwapExtras
    ).pairType;
    createPairPumpSwapDetailDto.baseVault = pairPumpSwapDto.baseVault;
    createPairPumpSwapDetailDto.quoteVault = pairPumpSwapDto.quoteVault;
    return createPairPumpSwapDetailDto;
  }

  async createPumpSwapPair(pumpSwapPairDto: CreatePairDto) {
    try {
      const pairToCreate: Partial<PairSchemaClass> = {
        baseAddress: pumpSwapPairDto.baseAddress,
        quoteAddress: pumpSwapPairDto.quoteAddress,
        pairAddress: pumpSwapPairDto.pairAddress,
        signatureHash: pumpSwapPairDto.signatureHash,
        creator: pumpSwapPairDto.creator,
        dexLabel: pumpSwapPairDto.dexLabel,
        extra: pumpSwapPairDto.extra,
        updatedAt: pumpSwapPairDto.blockTime,
        createdAt: pumpSwapPairDto.blockTime,
      };
      await this.pairDocumentRepository.create(pairToCreate as PairSchemaClass);
    } catch (err) {
      this.logger.error(
        `Failed to insert new PumpSwap Pool [${pumpSwapPairDto.signatureHash}] into database, reason: ${err.message}`,
      );
      this.logger.debug(err.stack);
      throw new Error(err);
    }
  }

  /**
  Pump.swap pair detail
   */

  async handleCreatePumpSwapPairDetail(
    createPairPumpSwapDetailDto: CreatePumpSwapPairDetailDto,
  ) {
    const existPairDetail =
      await this.pairDetailRepository.findOneByPairAddress(
        createPairPumpSwapDetailDto.pairAddress,
      );
    if (existPairDetail) {
      return;
    }
    const begin = performance.now();
    const pairDetailDto = await this.populatePairDetail(
      createPairPumpSwapDetailDto,
    );
    if (!pairDetailDto) {
      this.logger.error(
        `[FAILURE] Failed to generate Pair Detail for pump.swap pair address: [${createPairPumpSwapDetailDto.pairAddress}]`,
      );
      return;
    }
    await this.createPumpSwapPoolDetail(pairDetailDto);
    this.logger.verbose(
      `Stored Detailed Pair [${pairDetailDto.pairAddress}], elapsed time [${(performance.now() - begin).toFixed(0)}]ms.`,
    );
  }

  async handleCreatePumpSwapPairDetailTimeFrames(
    createPairPumpSwapDetailDto: CreatePumpSwapPairDetailDto,
  ) {
    const pairDetailTimeFrames =
      await this.pairService.populatePairDetailTimeframes(
        createPairPumpSwapDetailDto.pairAddress,
      );
    if (!pairDetailTimeFrames) {
      this.logger.error(
        `[FAILURE] Failed to generate pairDetailTimeframes for pump.swap pair address: ${createPairPumpSwapDetailDto.pairAddress}`,
      );
      return;
    }
    await Promise.all(
      pairDetailTimeFrames.map(async (pairDetailTimeframe) => {
        const savedPairDetailTimeframes =
          await this.pairDetailTimeFramesRepository.createPairDetailTimeframes(
            pairDetailTimeframe,
          );

        if (savedPairDetailTimeframes.status !== 'inserted') {
          this.logger.error(
            `[FAILURE] Failed to store pairDetailTimeframes: ${pairDetailTimeframe.timeInterval}; for Pump.swap pair: [${createPairPumpSwapDetailDto.pairAddress}]`,
          );
        } else {
          this.logger.log(
            `[SUCCESS] Successfully stored pairDetailTimeframes: ${pairDetailTimeframe.timeInterval}; for Pump.swap pair: [${createPairPumpSwapDetailDto.pairAddress}]`,
          );
        }
      }),
    );
  }

  async addUpdatePumpSwapPairDetailDtoToQueue(
    updatedPairsDetail: UpdatePumpSwapPairDetailDto[],
  ) {
    const filteredPair = [];
    const pumpSwapPair = await Promise.all(
      updatedPairsDetail.map(
        async (updatedPairsDetail) =>
          await this.pairDocumentRepository.findByPairAddress(
            updatedPairsDetail.pairAddress,
          ),
      ),
    );
    for (let i = 0; i < updatedPairsDetail.length; i++) {
      if (pumpSwapPair[i]) {
        filteredPair.push(updatedPairsDetail[i]);
      }
    }
    updatedPairsDetail = filteredPair;
    if (updatedPairsDetail.length < 1) {
      return;
    }
    await this.updateMultiplePoolQueue.add(
      PUMP_SWAP_UPDATE_PAIR_DETAIL_JOB,
      updatedPairsDetail,
    );
  }

  async handleUpdateMultiplePumpSwapPoolDetail(
    updatePumpswapPairDetailDtos: UpdatePumpSwapPairDetailDto[],
  ) {
    return await Promise.allSettled(
      updatePumpswapPairDetailDtos.map(async (updatedPairDetailDto) => {
        const lockKey = updatedPairDetailDto.pairAddress;
        const lock = await this.redisRepository.redlock.acquire(
          [lockKey],
          10000,
        );
        const begin = performance.now();
        try {
          const pairDetail =
            await this.populateUpdatePairDetail(updatedPairDetailDto);
          await this.pairDetailRepository.updatePairDetailPumpSwap(pairDetail);
        } catch (err) {
          throw err;
        } finally {
          this.logger.log(
            `[SUCCESS] Completed update pool [${updatedPairDetailDto.pairAddress}], elapsed: ${(performance.now() - begin).toFixed()}ms `,
          );
          await lock.release();
        }
      }),
    );
  }

  async handleUpdatePumpSwapDetailTimeframes(
    updatePumpSwapPairDetailDtos: UpdatePumpSwapPairDetailDto[],
  ) {
    return await Promise.allSettled(
      updatePumpSwapPairDetailDtos.map(
        async (pairDetailEntityAndUpdatedPoolDetailDto) => {
          const updatedPairDetailTimeframes =
            await this.pairService.populateUpdatedPairDetailTimeframes(
              pairDetailEntityAndUpdatedPoolDetailDto.pairAddress,
            );
          for (const updatedPairDetailTimeframe of updatedPairDetailTimeframes) {
            await this.pairDetailTimeFramesRepository.updatePairDetailTimeframes(
              updatedPairDetailTimeframe,
            );
          }
        },
      ),
    );
  }

  private async populatePairDetail(
    createPairPumpSwapDetailDto: CreatePumpSwapPairDetailDto,
  ) {
    try {
      const begin = performance.now();
      const pairPumpSwap = await this.pairDocumentRepository.findByPairAddress(
        createPairPumpSwapDetailDto.pairAddress,
      );
      if (!pairPumpSwap) {
        return null;
      }

      const result = await Promise.all([
        this.solanaService.getSolPrice(),
        this.connection.getTokenAccountBalance(
          new PublicKey(createPairPumpSwapDetailDto.baseVault),
        ),
        this.connection.getBalance(
          new PublicKey(createPairPumpSwapDetailDto.quoteVault),
        ),
        this.solanaService.getTokenAccountInfo(
          createPairPumpSwapDetailDto.baseAddress,
        ),
        this.swapTransactionPumpSwapService.getPumpSwapTokenBuySellForVolumeAndTxns(
          createPairPumpSwapDetailDto.pairAddress,
        ),
        this.swapTransactionPumpSwapService.findVol24hByPairAddress(
          createPairPumpSwapDetailDto.pairAddress,
        ),
        this.swapTransactionPumpSwapService.getAllPairBuyerAndSellerByPairAddress(
          createPairPumpSwapDetailDto.pairAddress,
        ),
      ]);

      const solPriceUsd = result[0];
      const tokenAccountBalance = result[1];
      const balance = result[2] / LAMPORTS_PER_SOL;
      const tokenAccountInfo = result[3];
      const total = Number(tokenAccountBalance.value.uiAmount);
      const { totalTxns, totalVolume } = result[4];
      const volume24h = result[5];
      const numberOfBuyerAndSeller = result[6];
      let priceUsd = (balance / total) * solPriceUsd;
      if (total === 0) {
        priceUsd = 0;
      }
      const pairDetailDto = new PairPumpSwapDetailDto();
      pairDetailDto.priceUsd = Number(priceUsd.toFixed(9));
      pairDetailDto.pooledSOL = Number(balance.toFixed(9));
      pairDetailDto.liquidityUsd = Number(
        (balance * solPriceUsd + total * priceUsd).toFixed(9),
      );
      pairDetailDto.marketCapUsd = Number(
        (Number(tokenAccountInfo.supply) * priceUsd).toFixed(9),
      );
      pairDetailDto.pairAddress = createPairPumpSwapDetailDto.pairAddress;
      pairDetailDto.totalTxns = totalTxns;
      pairDetailDto.totalVolume = totalVolume;
      pairDetailDto.volume24h = volume24h;
      pairDetailDto.numOfBuyers = numberOfBuyerAndSeller.numOfBuyers;
      pairDetailDto.numOfSellers = numberOfBuyerAndSeller.numOfSellers;
      this.logger.verbose(
        `Stored Detailed Pair [${pairDetailDto.pairAddress}], elapsed time [${(performance.now() - begin).toFixed(0)}]ms.`,
      );
      return pairDetailDto;
    } catch (error) {
      this.logger.error(error);
    }
  }

  private async populateUpdatePairDetail(
    updatePumpswapPairDetailDto: UpdatePumpSwapPairDetailDto,
  ) {
    try {
      const [
        solPriceUsd,
        _topHolderPercentages,
        { totalTxns, totalVolume },
        volume24h,
        numberOfBuyerAndSeller,
      ] = await Promise.all([
        this.solanaService.getSolPrice(),
        this.calculateTopHolders(
          updatePumpswapPairDetailDto.baseMint,
          updatePumpswapPairDetailDto.pairAddress,
        ),
        this.swapTransactionPumpSwapService.getPumpSwapTokenBuySellForVolumeAndTxns(
          updatePumpswapPairDetailDto.pairAddress,
        ),
        this.swapTransactionPumpSwapService.findVol24hByPairAddress(
          updatePumpswapPairDetailDto.pairAddress,
        ),
        this.swapTransactionPumpSwapService.getAllPairBuyerAndSellerByPairAddress(
          updatePumpswapPairDetailDto.pairAddress,
        ),
      ]);

      const poolQuoteTokenReserves = new BigNumber(
        updatePumpswapPairDetailDto.poolQuoteTokenReserves,
      );
      const poolBaseTokenReserves = new BigNumber(
        updatePumpswapPairDetailDto.poolBaseTokenReserves,
      );
      const baseTokens = poolBaseTokenReserves;
      const quoteTokens = poolQuoteTokenReserves;

      let pooledSol = BigNumber(0);
      let priceUsd = BigNumber(0);
      let marketCapUsd = BigNumber(0);
      if (!quoteTokens.isEqualTo(0)) {
        pooledSol = baseTokens.dividedBy(quoteTokens);
        priceUsd = baseTokens
          .div(quoteTokens)
          .multipliedBy(PUMPSWAP_AND_SOL_DIFFERENTIAL)
          .multipliedBy(solPriceUsd);
        marketCapUsd = quoteTokens
          .div(baseTokens)
          .multipliedBy(PUMPSWAP_AND_SOL_DIFFERENTIAL)
          .multipliedBy(solPriceUsd)
          .multipliedBy(LAMPORTS_PER_SOL);
      }
      return {
        pairAddress: updatePumpswapPairDetailDto.pairAddress,
        pooledSol: Number(pooledSol.toFixed(9)),
        priceUsd: Number(priceUsd.toNumber().toFixed(9)),
        marketCapUsd: Number(marketCapUsd.toNumber().toFixed(9)),
        numOfBuyers: numberOfBuyerAndSeller.numOfBuyers,
        numOfSellers: numberOfBuyerAndSeller.numOfSellers,
        buyVolumeUsd: totalVolume.buyTotalVolume,
        sellVolumeUsd: totalVolume.sellTotalVolume,
        numOfBuyTxs: totalTxns.buyTotalTxns,
        numOfSellTxs: totalTxns.sellTotalTxns,
        process: 100,
        volume24h: volume24h,
        liquidityUsd: new BigNumber(pooledSol)
          .dividedBy(new BigNumber(LAMPORTS_PER_SOL))
          .multipliedBy(new BigNumber(solPriceUsd))
          .toNumber(),
        createdAt: new Date(),
        updatedAt: new Date(),
      } as PairDetail;
    } catch (error) {
      this.logger.error(`Error calculating pool details: ${error.message}`);
      return null;
    }
  }

  private async createPumpSwapPoolDetail(poolDetailDto: PairPumpSwapDetailDto) {
    const pairPumpSwapDetail = new PairDetail();
    pairPumpSwapDetail.pairAddress = poolDetailDto.pairAddress;
    pairPumpSwapDetail.pooledSol = poolDetailDto.pooledSOL;
    pairPumpSwapDetail.marketCapUsd = poolDetailDto.marketCapUsd;
    pairPumpSwapDetail.priceUsd = poolDetailDto.priceUsd;
    pairPumpSwapDetail.liquidityUsd = poolDetailDto.liquidityUsd;
    pairPumpSwapDetail.numOfBuyTxs = poolDetailDto.totalTxns.buyTotalTxns;
    pairPumpSwapDetail.numOfSellTxs = poolDetailDto.totalTxns.sellTotalTxns;
    pairPumpSwapDetail.buyVolumeUsd = poolDetailDto.totalVolume.buyTotalVolume;
    pairPumpSwapDetail.sellVolumeUsd =
      poolDetailDto.totalVolume.sellTotalVolume;
    pairPumpSwapDetail.numOfBuyers = poolDetailDto.numOfBuyers;
    pairPumpSwapDetail.numOfSellers = poolDetailDto.numOfSellers;
    pairPumpSwapDetail.createdAt = new Date();
    pairPumpSwapDetail.updatedAt = new Date();
    pairPumpSwapDetail.process = 100;
    pairPumpSwapDetail.ts = new Date().getTime();
    return await this.pairDetailRepository.createPairDetail(pairPumpSwapDetail);
  }

  private async calculateTopHolders(
    tokenAddress: string,
    associatedBondingCurve: string,
  ) {
    const [holders, totalSupply] = await Promise.all([
      await this.solanaService.getTokenTopHolders(tokenAddress, 20),
      await this.solanaService.getTokenSupply(tokenAddress),
    ]);

    // Take top 10 and calculate percentage
    const holdersPercentage = holders
      .filter(
        (resultHolder) =>
          resultHolder.address.toString() !== associatedBondingCurve &&
          resultHolder.uiAmount > 0,
      )
      .slice(0, 10)
      .map((holder) => {
        if (Number(totalSupply.amount) !== 0) {
          return {
            owner: holder.address,
            balance: holder.amount,
            decimals: holder.decimals,
            percentage: BigNumber(holder.amount).div(totalSupply.amount),
          };
        }
        return {
          owner: holder.address,
          balance: holder.amount,
          decimals: holder.decimals,
          percentage: BigNumber(0),
        };
      });

    return holdersPercentage
      .reduce((sum, holder) => sum.plus(holder.percentage), new BigNumber(0))
      .dp(5)
      .toNumber();
  }
}
