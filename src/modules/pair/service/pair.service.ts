import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { TokenService } from '../../token/token.service';
import {
  ASSOCIATED_TOKEN_PROGRAM_ID,
  TOKEN_PROGRAM_ID,
} from '@solana/spl-token';
import { PairDetailRepository } from '../infrastructure/persistence/time-series/repositories/pair-details.repository';
import { Connection, LAMPORTS_PER_SOL, PublicKey } from '@solana/web3.js';
import { BONDING_CURVE_LAYOUT } from '../../../common/layout/bonding-curve';
import BigNumber from 'bignumber.js';
import { SolanaService } from '../../solana/solana.service';
import { TxnsCount } from 'src/modules/swap-transaction/dto/txns-count.dto';
import { VolumeResult } from 'src/modules/swap-transaction/dto/volume-result.dto';
import { CreatePairDto, PumpfunExtra } from '../dto/create-pair.dto';
import { PairSchemaClass } from '../infrastructure/persistence/document/entities/pair.schema';
import { NewPairDto } from '../dto/new-pair.dto';
import { PairDetailTimeframesRepository } from '../infrastructure/persistence/time-series/repositories/pair-details-timeframes.repository';
import {
  PairDetailTimeframes,
  TimeInterval,
} from 'src/modules/pair/domain/pair-detail-timeframes';
import { PairNotFoundException } from 'src/common/exceptions/pair-not-found-exception';
import { TokenNotFoundException } from 'src/common/exceptions/token-not-found-exception';
import { PairTimeFramesDto } from '../dto/pair-timeframes.dto';
import { PairDto } from '../dto/pair.dto';
import { BuyerSellerResult } from 'src/modules/swap-transaction/dto/buyer-seller-result.dto';
import {
  PUMPFUN_AND_SOL_DIFFERENTIAL,
  PUMPFUN_PROGRAM_ID,
  PUMPFUN_TO_RAYDIUM_MIGRATION_AMOUNT,
} from '../../../common/constant/pumpfun/pumpfun';
import { SwapTransactionService } from '../../swap-transaction/services/swap-transaction.service';
import { PairDocumentRepository } from '../infrastructure/persistence/document/repositories/pair-document.repository';
import { DexLabelService } from '../../dex-label/dex-label.service';
import { DexService } from '../../dex/dex.service';
import { DexLabelNotFoundException } from '../../../common/exceptions/dex-label-not-found-exception';
import { PairDocument } from '../domain/pair-document';
import { DexNotFoundException } from '../../../common/exceptions/dex-not-found-exception';
import { TokenDto } from 'src/modules/token/dto/token.dto';
import { DexLabelDto } from '../../dex-label/dto/dex-label.dto';
import { SortDirection } from '../../../common/constant/sort-direction';
import { PairDetail } from '../domain/pair-detail';
import { NullableType } from '../../../common/type/nullable.type';
import { PairDocumentMapper } from '../infrastructure/persistence/document/mappers/pair-document.mapper';
import { sortableFields } from '../dto/get-recent-pairs.dto';
import { DexName } from 'src/common/constant/solana';
import { TokenMetricsException } from 'src/common/exceptions/token-metrics-exception';
import { TokenAmountUnavailableException } from 'src/common/exceptions/token-amount-unavailable-exception';

@Injectable()
export class PairService {
  private readonly logger: Logger = new Logger(PairService.name);

  constructor(
    @Inject()
    private readonly pairDocumentRepository: PairDocumentRepository,
    @Inject()
    private readonly pairDetailRepository: PairDetailRepository,
    @Inject()
    private readonly pairDetailTimeFramesRepository: PairDetailTimeframesRepository,
    @Inject()
    private readonly tokenService: TokenService,
    @Inject('SolanaConnection')
    private readonly connection: Connection,
    @Inject()
    private readonly solanaService: SolanaService,
    @Inject()
    private readonly dexLabelService: DexLabelService,
    @Inject()
    private readonly dexService: DexService,
    @Inject(forwardRef(() => SwapTransactionService))
    private readonly swapTransactionService: SwapTransactionService,
  ) {}

  async getPairsByAddress(pairAddress: string): Promise<PairDto> {
    // Find Pair
    const pair =
      await this.pairDocumentRepository.findByPairAddress(pairAddress);
    if (!pair) {
      throw new PairNotFoundException(pairAddress);
    }

    // Find Token
    const baseToken = await this.tokenService.getTokenByAddress(
      pair.baseAddress,
    );
    if (!baseToken) {
      throw new TokenNotFoundException(pair.baseAddress);
    }
    const quoteToken = await this.tokenService.getTokenByAddress(
      pair.quoteAddress,
    );
    if (!quoteToken) {
      throw new TokenNotFoundException(pair.quoteAddress);
    }

    // Find Pair Details
    const pairDetail =
      await this.pairDetailRepository.findOneByPairAddress(pairAddress);
    if (!pairDetail) {
      throw new PairNotFoundException(pairAddress);
    }

    // Find Pair Details Timeframes
    const timeIntervals: TimeInterval[] = Object.values(TimeInterval);
    const timeframes: Record<string, PairTimeFramesDto> = {};
    for (const interval of timeIntervals) {
      const pairDetailTimeFrames =
        await this.pairDetailTimeFramesRepository.findPairDetailsTimeframesByPairAddress(
          pairAddress,
          interval,
        );
      if (pairDetailTimeFrames) {
        timeframes[interval] = {
          buyVolumeUsd: pairDetailTimeFrames.buyVolumeUsd,
          sellVolumeUsd: pairDetailTimeFrames.sellVolumeUsd,
          numOfBuyers: pairDetailTimeFrames.numOfBuyers,
          numOfSellers: pairDetailTimeFrames.numOfSellers,
          numOfBuyTxs: pairDetailTimeFrames.numOfBuyTxs,
          numOfSellTxs: pairDetailTimeFrames.numOfSellTxs,
          updatedAt: pairDetailTimeFrames.updatedAt.getTime().toString(),
          ts: pairDetailTimeFrames.ts.getTime().toString(),
        };
      }
    }

    // Find Dex Label
    const dexLabel = await this.dexLabelService.findByDexLabel(pair.dexLabel);
    if (!dexLabel) {
      throw new DexLabelNotFoundException(pair.dexLabel);
    }

    // Find Dex
    const dex = await this.dexService.findById(dexLabel.dexId);
    if (!dex) {
      throw new DexNotFoundException(dexLabel.dexId);
    }

    const baseTokenDto: TokenDto = {
      address: baseToken.tokenAddress,
      name: baseToken.tokenName,
      image: baseToken.image,
      bannerImage: baseToken.bannerImage ?? '',
      symbol: baseToken.symbol,
      twitter: baseToken.twitter ?? '',
      website: baseToken.website ?? '',
      telegram: baseToken.telegram ?? '',
    };

    const quoteTokenDto: TokenDto = {
      address: quoteToken.tokenAddress,
      name: quoteToken.tokenName,
      image: quoteToken.image,
      bannerImage: quoteToken.bannerImage ?? '',
      symbol: quoteToken.symbol,
      twitter: quoteToken.twitter ?? '',
      website: quoteToken.website ?? '',
      telegram: quoteToken.telegram ?? '',
    };

    const dexDto: DexLabelDto = {
      dex: dex.name,
      label: dexLabel.label,
      programAddress: dexLabel.programAddress,
      description: dexLabel.description ?? '',
    };

    return {
      pairAddress: pairAddress,
      createdAt: pair.createdAt.toISOString(),
      baseToken: baseTokenDto,
      quoteToken: quoteTokenDto,
      dex: dexDto,
      creator: pair.creator,
      liquidityUsd: pairDetail?.liquidityUsd ?? 0,
      buyVolumeUsd: pairDetail?.buyVolumeUsd ?? 0,
      sellVolumeUsd: pairDetail?.sellVolumeUsd ?? 0,
      numOfBuyTxs: pairDetail?.numOfBuyTxs ?? 0,
      numOfSellTxs: pairDetail?.numOfSellTxs ?? 0,
      process: pairDetail?.process ?? 0,
      marketCapUsd: pairDetail?.marketCapUsd ?? 0,
      extra: pair.extra,
      timeframes,
    };
  }

  async getRecentNewPairsWithin24h(
    limit: number,
    page = 1,
    filterBy?: DexName,
    sortBy?: string,
    sortDirection: 'asc' | 'desc' = 'desc',
  ): Promise<NewPairDto[]> {
    const since = Date.now() - 24 * 60 * 60 * 1000;
    const skip = (page - 1) * limit;

    const sortField = sortableFields.includes(sortBy || '') ? sortBy : 'ts';
    const direction =
      sortDirection === 'asc' ? SortDirection.ASC : SortDirection.DESC;

    const recentPairs = await this.pairDocumentRepository.findRecentPairsSince(
      since,
      limit,
      skip,
      filterBy,
    );

    if (recentPairs.length === 0) return [];

    const pairAddresses = recentPairs.map((p) => p.pairAddress);
    const tokenAddresses = recentPairs.map((p) => p.baseAddress);

    const [tokens, pairDetails] = await Promise.all([
      this.tokenService.getTokensByAddresses(tokenAddresses),
      this.pairDetailRepository.findLastestPairDetails(
        pairAddresses,
        sortField,
        direction,
      ),
    ]);

    const tokenMap = new Map(tokens.map((t) => [t.tokenAddress, t]));
    const pairMap = new Map(recentPairs.map((p) => [p.pairAddress, p]));

    const results = await Promise.allSettled(
      pairDetails.map(async (detail: PairDetail) => {
        const pair = pairMap.get(detail.pairAddress);
        if (!pair) throw new PairNotFoundException(pair.pairAddress);

        const token = tokenMap.get(pair.baseAddress);
        if (!token) throw new TokenNotFoundException(token.tokenAddress);

        const [devHoldsPercentResult] = await Promise.allSettled([
          this.getPercentageDevHolding(token.tokenAddress),
        ]);

        let devHoldsPercent = 0;
        if (devHoldsPercentResult.status === 'fulfilled') {
          devHoldsPercent = devHoldsPercentResult.value;
        } else {
          this.logger.error(
            `Failed to get dev holding % for token ${token.tokenAddress} (${token.tokenName}) in pair ${pair.pairAddress}:`,
            devHoldsPercentResult.reason,
          );
        }

        return {
          pairAddress: pair.pairAddress,
          createdAt: pair.createdAt.toString(),
          dexLabel: pair.dexLabel,

          tokenName: token.tokenName,
          tokenAddress: token.tokenAddress,
          image: token.image,
          twitter: token.twitter,
          website: token.website,
          telegram: token.telegram,
          symbol: token.symbol,

          liquidityUsd: detail.liquidityUsd,
          buyVolumeUsd: detail.buyVolumeUsd,
          sellVolumeUsd: detail.sellVolumeUsd,
          numOfBuyTxs: detail.numOfBuyTxs,
          numOfSellTxs: detail.numOfSellTxs,
          numOfBuyers: detail.numOfBuyers,
          numOfSellers: detail.numOfSellers,
          process: detail.process,
          marketCapUsd: detail.marketCapUsd,
          devHoldsPercent,
          // TODO: temporarily fallback to 0 until next implementation
          top10HoldersPercent: 0,
          snipersHoldPercent: 0,
          insidersHoldPercent: 0,
          bundlersHoldPercent: 0,
        };
      }),
    );

    results
      .filter((result) => result.status === 'rejected')
      .forEach((result, idx) =>
        this.logger.error(
          `pairDetails mapping failed at index ${idx}:`,
          result.reason,
        ),
      );

    return results
      .filter((result) => result.status === 'fulfilled')
      .map((result) => result.value);
  }

  async populatePairDetail(
    pair: PairDocument,
  ): Promise<NullableType<PairDetail>> {
    try {
      const bondingCurve = new PublicKey(pair.pairAddress);
      const pairAddress = pair.pairAddress;

      let solPriceUsd = null;
      let accountInfoRaw = null;
      let rawPooledSol = null;
      let buySellCountsTxns: TxnsCount = null;
      let buySellVolume: VolumeResult = null;
      let buyerSeller: BuyerSellerResult = null;

      const results = await Promise.allSettled([
        this.solanaService.getSolPrice(),
        this.connection.getAccountInfo(bondingCurve),
        this.connection.getBalance(bondingCurve),

        this.swapTransactionService.getBuySellCountsTxnsByPairAddress(
          pairAddress,
        ),
        this.swapTransactionService.getBuySellVolumeByPairAddress(pairAddress),
        this.swapTransactionService.getAllPairBuyerAndSellerByPairAddress(
          bondingCurve.toBase58(),
        ),
        // this.swapTransactionPumpfunService.getVol24hByMint(tokenAddress),
      ]);

      if (results[0].status === 'fulfilled') solPriceUsd = results[0].value;
      if (results[1].status === 'fulfilled') accountInfoRaw = results[1].value;
      if (results[2].status === 'fulfilled') rawPooledSol = results[2].value;

      if (results[3].status === 'fulfilled') {
        buySellCountsTxns = results[3].value;
      }
      if (results[4].status === 'fulfilled') {
        buySellVolume = results[4].value;
      }
      if (results[5].status === 'fulfilled') {
        buyerSeller = results[5].value;
      }

      if (!accountInfoRaw) throw new Error('Account Info not found');

      const accountInfo = BONDING_CURVE_LAYOUT.decode(
        accountInfoRaw.data.slice(8),
      );
      const virtualTokenReserves = BigNumber(
        accountInfo.virtualTokenReserves.toString(),
      );
      const virtualSolReserves = BigNumber(
        accountInfo.virtualSolReserves.toString(),
      );
      const realTokenReserves = BigNumber(
        accountInfo.realTokenReserves.toString(),
      );

      const priceUsd = virtualSolReserves
        .div(virtualTokenReserves)
        .multipliedBy(PUMPFUN_AND_SOL_DIFFERENTIAL)
        .multipliedBy(solPriceUsd)
        .toNumber();

      const marketCapUsd = priceUsd * LAMPORTS_PER_SOL;
      const liquidityUsd = new BigNumber(rawPooledSol)
        .dividedBy(new BigNumber(LAMPORTS_PER_SOL))
        .multipliedBy(new BigNumber(solPriceUsd))
        .toNumber();
      const process = BigNumber(1)
        .minus(
          BigNumber(100 / PUMPFUN_TO_RAYDIUM_MIGRATION_AMOUNT)
            .multipliedBy(realTokenReserves)
            .div(100),
        )
        .dp(5)
        .toNumber();
      const pooledSol = new BigNumber(rawPooledSol)
        .dividedBy(new BigNumber(LAMPORTS_PER_SOL))
        .toNumber();

      const blockTimeInMs = Date.parse(pair.createdAt.toString());
      if (isNaN(blockTimeInMs)) {
        this.logger.error('Failed to parse pair blockTime.');
        return null;
      }

      return {
        pairAddress: pair.pairAddress,
        marketCapUsd,
        liquidityUsd,
        buyVolumeUsd: buySellVolume?.buyTotalVolume ?? 0,
        sellVolumeUsd: buySellVolume?.sellTotalVolume ?? 0,
        numOfBuyTxs: buySellCountsTxns?.buyTotalTxns ?? 0,
        numOfSellTxs: buySellCountsTxns?.sellTotalTxns ?? 0,
        numOfBuyers: buyerSeller?.numOfBuyers ?? 0,
        numOfSellers: buyerSeller?.numOfSellers ?? 0,
        process,
        priceUsd,
        pooledSol,
        createdAt: new Date(),
        updatedAt: new Date(),
        ts: blockTimeInMs,
      };
    } catch (error) {
      this.logger.error(`Error calculating pair detail: ${error.message}`);
      return null;
    }
  }

  async populateUpdatedPairDetail(
    tokenAddress: string,
    pairAddress: string,
  ): Promise<PairDetail | undefined> {
    try {
      if (!pairAddress) {
        this.logger.warn(
          `populateUpdatedPairDetail:pairAddress is empty, skipping`,
        );
        return;
      }
      const bondingCurve = new PublicKey(pairAddress);
      const mintAccount = new PublicKey(tokenAddress).toBytes();

      const [associatedBondingCurve] = PublicKey.findProgramAddressSync(
        [bondingCurve.toBuffer(), TOKEN_PROGRAM_ID.toBuffer(), mintAccount],
        ASSOCIATED_TOKEN_PROGRAM_ID,
      );

      let solPriceUsd = null;
      let accountInfoRaw = null;
      let rawPooledSol = null;
      let _topHolderPercentages = null;
      let buySellCountsTxns: TxnsCount = null;
      let buySellVolume: VolumeResult = null;
      let buyerSeller: BuyerSellerResult = null;

      const results = await Promise.allSettled([
        this.solanaService.getSolPrice(),
        this.connection.getAccountInfo(bondingCurve),
        this.connection.getBalance(bondingCurve),
        this.calculateTopHolders(
          tokenAddress,
          associatedBondingCurve.toBase58(),
        ),
        this.swapTransactionService.getBuySellCountsTxnsByPairAddress(
          pairAddress,
        ),
        this.swapTransactionService.getBuySellVolumeByPairAddress(pairAddress),
        this.swapTransactionService.getAllPairBuyerAndSellerByPairAddress(
          bondingCurve.toBase58(),
        ),
        // this.swapTransactionPumpfunService.getVol24hByMint(tokenAddress),
      ]);

      if (results[0].status === 'fulfilled') solPriceUsd = results[0].value;
      if (results[1].status === 'fulfilled') accountInfoRaw = results[1].value;
      if (results[2].status === 'fulfilled') rawPooledSol = results[2].value;
      if (results[3].status === 'fulfilled')
        _topHolderPercentages = results[3].value; // TODO: Implement this field
      if (results[4].status === 'fulfilled') {
        buySellCountsTxns = results[4].value;
      }
      if (results[5].status === 'fulfilled') {
        buySellVolume = results[5].value;
      }
      if (results[6].status === 'fulfilled') {
        buyerSeller = results[6].value;
      }

      if (!accountInfoRaw) throw new Error('Account Info not found');
      const accountInfo = BONDING_CURVE_LAYOUT.decode(
        accountInfoRaw.data.slice(8),
      );

      const virtualTokenReserves = BigNumber(
        accountInfo.virtualTokenReserves.toString(),
      );
      const virtualSolReserves = BigNumber(
        accountInfo.virtualSolReserves.toString(),
      );
      const realTokenReserves = BigNumber(
        accountInfo.realTokenReserves.toString(),
      );
      const liquidityUsd = new BigNumber(rawPooledSol)
        .dividedBy(new BigNumber(LAMPORTS_PER_SOL))
        .multipliedBy(new BigNumber(solPriceUsd))
        .toNumber();
      const process = BigNumber(1)
        .minus(
          BigNumber(100 / PUMPFUN_TO_RAYDIUM_MIGRATION_AMOUNT)
            .multipliedBy(realTokenReserves)
            .div(100),
        )
        .dp(5)
        .toNumber();
      const priceUsd = virtualSolReserves
        .div(virtualTokenReserves)
        .multipliedBy(PUMPFUN_AND_SOL_DIFFERENTIAL)
        .multipliedBy(solPriceUsd)
        .toNumber();
      const marketCapUsd = priceUsd * LAMPORTS_PER_SOL;
      const pooledSol = new BigNumber(rawPooledSol)
        .dividedBy(new BigNumber(LAMPORTS_PER_SOL))
        .toNumber();

      return {
        pairAddress,
        pooledSol,
        priceUsd,
        marketCapUsd,
        process,
        liquidityUsd,
        buyVolumeUsd: buySellVolume?.buyTotalVolume ?? 0,
        sellVolumeUsd: buySellVolume?.sellTotalVolume ?? 0,
        numOfBuyTxs: buySellCountsTxns?.buyTotalTxns ?? 0,
        numOfSellTxs: buySellCountsTxns?.sellTotalTxns ?? 0,
        numOfBuyers: buyerSeller?.numOfBuyers ?? 0,
        numOfSellers: buyerSeller?.numOfSellers ?? 0,
        createdAt: undefined,
        updatedAt: undefined,
        ts: 0,
      };
    } catch (error) {
      this.logger.error(`Error calculating pair details: ${error.message}`);
      return null;
    }
  }

  async populatePairDetailTimeframes(
    pairAddress: string,
  ): Promise<PairDetailTimeframes[] | null> {
    try {
      const pairDetailTimeframes: PairDetailTimeframes[] = [];
      const timeIntervals: TimeInterval[] = Object.values(TimeInterval);
      for (const interval of timeIntervals) {
        const pairDetailMetrics =
          await this.swapTransactionService.getAllPairDerailTimeframesByAddressAndTimeframes(
            pairAddress,
            interval,
          );

        if (!!pairDetailMetrics) {
          pairDetailTimeframes.push({
            pairAddress: pairAddress,
            buyVolumeUsd: pairDetailMetrics.buyVolumeUsd,
            numOfBuyTxs: pairDetailMetrics.numOfBuyTxs,
            numOfBuyers: pairDetailMetrics.numOfBuyers,
            numOfSellTxs: pairDetailMetrics.numOfSellTxs,
            numOfSellers: pairDetailMetrics.numOfSellers,
            sellVolumeUsd: pairDetailMetrics.sellVolumeUsd,
            timeInterval: interval,
            updatedAt: new Date(),
            ts: new Date(),
          });
        } else {
          pairDetailTimeframes.push({
            pairAddress: pairAddress,
            buyVolumeUsd: 0,
            numOfBuyTxs: 0,
            numOfBuyers: 0,
            numOfSellTxs: 0,
            numOfSellers: 0,
            sellVolumeUsd: 0,
            timeInterval: interval,
            updatedAt: new Date(),
            ts: new Date(),
          });
        }
      }
      return pairDetailTimeframes;
    } catch (error) {
      this.logger.error(`Error calculating pair detail: ${error.message}`);
      return null;
    }
  }

  async populateUpdatedPairDetailTimeframes(
    pairAddress: string,
  ): Promise<PairDetailTimeframes[] | null> {
    try {
      const pairDetailTimeframes: PairDetailTimeframes[] = [];
      const timeIntervals: TimeInterval[] = Object.values(TimeInterval);
      for (const interval of timeIntervals) {
        const pairDetailMetrics =
          await this.swapTransactionService.getAllPairDerailTimeframesByAddressAndTimeframes(
            pairAddress,
            interval,
          );

        if (!!pairDetailMetrics) {
          pairDetailTimeframes.push({
            pairAddress: pairAddress,
            buyVolumeUsd: pairDetailMetrics.buyVolumeUsd,
            numOfBuyTxs: pairDetailMetrics.numOfBuyTxs,
            numOfBuyers: pairDetailMetrics.numOfBuyers,
            numOfSellTxs: pairDetailMetrics.numOfSellTxs,
            numOfSellers: pairDetailMetrics.numOfSellers,
            sellVolumeUsd: pairDetailMetrics.sellVolumeUsd,
            timeInterval: interval,
            updatedAt: new Date(),
            ts: new Date(),
          });
        }
      }
      return pairDetailTimeframes;
    } catch (error) {
      this.logger.error(`Error calculating pair detail: ${error.message}`);
      return null;
    }
  }

  private async calculateTopHolders(
    tokenAddress: string,
    associatedBondingCurve: string,
  ) {
    const [holders, totalSupply] = await Promise.all([
      await this.solanaService.getTokenTopHolders(tokenAddress, 20),
      await this.solanaService.getTokenSupply(tokenAddress),
    ]);

    // Take top 10 and calculate percentage
    const holdersPercentage = holders
      .filter(
        (resultHolder) =>
          resultHolder.address.toString() !== associatedBondingCurve &&
          resultHolder.uiAmount > 0,
      )
      .slice(0, 10)
      .map((holder) => ({
        owner: holder.address,
        balance: holder.amount,
        decimals: holder.decimals,
        percentage: BigNumber(holder.amount).div(totalSupply.amount),
      }));

    return holdersPercentage
      .reduce((sum, holder) => sum.plus(holder.percentage), new BigNumber(0))
      .dp(5)
      .toNumber();
  }

  async savePair(createPairDto: CreatePairDto): Promise<PairDocument | null> {
    const {
      baseAddress,
      quoteAddress,
      extra,
      pairAddress,
      signatureHash,
      dexLabel,
      creator,
      blockTime,
    } = createPairDto;

    try {
      const existPair =
        await this.pairDocumentRepository.findByBaseAndQuoteTokenAddress(
          baseAddress,
          quoteAddress,
        );

      if (existPair) {
        this.logger.debug(
          `Pair already exists for token address: ${baseAddress}/${quoteAddress}`,
        );
        return null;
      }
      const pairToCreate: Partial<PairSchemaClass> = {
        associatedBondingCurve: (extra as PumpfunExtra).associatedBondingCurve,
        baseAddress: baseAddress,
        quoteAddress: quoteAddress,
        pairAddress,
        signatureHash,
        dexLabel,
        creator,
        createdAt: blockTime,
        extra: {
          associatedBondingCurve: (extra as PumpfunExtra)
            .associatedBondingCurve,
        },
      };

      const savedPair = await this.pairDocumentRepository.create(
        pairToCreate as PairSchemaClass,
      );

      this.logger.log(
        `[SUCCESS] Completed storing pair for token: ${baseAddress}/${quoteAddress}`,
      );

      return PairDocumentMapper.toDomain(savedPair);
    } catch (error) {
      this.logger.error(
        `[FAILURE] Failed to save pair: ${error.message}`,
        error.stack,
      );
      return null;
    }
  }

  private _findBondingCurveByTokenAddress(tokenAddress: string) {
    const mintAccount = new PublicKey(tokenAddress).toBytes();
    const [bondingCurve] = PublicKey.findProgramAddressSync(
      [Buffer.from('bonding-curve'), mintAccount],
      new PublicKey(PUMPFUN_PROGRAM_ID),
    );
    return bondingCurve;
  }

  async getPairByPairAddress(pairAddress: string): Promise<PairDocument> {
    return await this.pairDocumentRepository.findByPairAddress(pairAddress);
  }

  async getPercentageDevHolding(pairAddress: string): Promise<number> {
    if (!pairAddress) {
      throw new TokenMetricsException(
        pairAddress,
        'Missing pairAddress parameter',
      );
    }

    const pair = await this.getPairByPairAddress(pairAddress);
    if (!pair) {
      throw new PairNotFoundException(pairAddress);
    }

    const token = await this.tokenService.getTokenByAddress(pair.baseAddress);
    if (!token) {
      throw new TokenNotFoundException(pair.baseAddress);
    }

    const { tokenAddress, creator } = token;

    const [amount, totalSupply] = await Promise.all([
      this.solanaService.getTokenAmountByOwner(creator, tokenAddress),
      this.solanaService.getTokenSupply(tokenAddress),
    ]);

    if (!totalSupply?.amount) {
      this.logger.error(
        `Token supply is not available for tokenAddress ${tokenAddress}`,
      );
      throw new TokenMetricsException(
        tokenAddress,
        'Missing devHolding metric',
      );
    }

    if (amount === undefined || amount === null) {
      this.logger.error(
        `Token amount is not available for tokenAddress ${tokenAddress}`,
      );
      throw new TokenAmountUnavailableException(tokenAddress);
    }

    return BigNumber(amount)
      .div(totalSupply.amount)
      .multipliedBy(100)
      .toNumber();
  }
}
