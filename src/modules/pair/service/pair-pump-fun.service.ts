import { Inject, Injectable, Logger } from '@nestjs/common';
import { TransactionData } from '../../../common/type/transaction.data';
import {
  PUMPFUN_CREATE_LOG_MESSAGE,
  PUMPFUN_PROGRAM_ID,
} from '../../../common/constant/pumpfun/pumpfun';
import _ from 'lodash';
import { CreatePumpfunPairRequestDto } from '../dto/create-pumpfun-pair-request.dto';
import { DexLabelNotFoundException } from '../../../common/exceptions/dex-label-not-found-exception';
import {
  PUMP_FUN_NEW_PAIR_TRANSACTION_JOB,
  PUMP_FUN_UPDATE_PAIR_JOB,
} from '../../../common/constant/job-name';
import { WSOL_ADDRESS } from '../../../common/constant/solana';
import { CreatePairDto, PumpfunExtra } from '../dto/create-pair.dto';
import { PairData } from '../../swap-transaction/dto/swap-transaction.dto';
import { PairDocument } from '../domain/pair-document';
import { PairDetailRepository } from '../infrastructure/persistence/time-series/repositories/pair-details.repository';
import { RedisRepository } from '../../redis/redis.repository';
import { PairDetailTimeframesRepository } from '../infrastructure/persistence/time-series/repositories/pair-details-timeframes.repository';
import { TokenService } from '../../token/token.service';
import { InjectQueue } from '@nestjs/bullmq';
import { PumpFunQueue } from '../../../common/constant/queue-name';
import { Queue } from 'bullmq';
import { DexLabelService } from '../../dex-label/dex-label.service';
import { PairService } from './pair.service';

@Injectable()
export class PairPumpFunService {
  private readonly logger: Logger = new Logger(PairPumpFunService.name);

  constructor(
    @Inject()
    private readonly pairDetailRepository: PairDetailRepository,
    @Inject()
    private readonly redisRepository: RedisRepository,
    @Inject()
    private readonly pairDetailTimeFramesRepository: PairDetailTimeframesRepository,
    @Inject()
    private readonly pairService: PairService,
    @Inject()
    private readonly tokenService: TokenService,
    @Inject()
    private readonly dexLabelService: DexLabelService,
    @InjectQueue(PumpFunQueue.NEW_PAIR)
    private readonly newPairQueue: Queue,
    @InjectQueue(PumpFunQueue.UPDATE_MULTIPLE_PAIR)
    private readonly updateMultiplePairQueue: Queue,
  ) {}

  async handlePumpFunPair(
    parsedTransactionData: TransactionData,
    blockTime: number,
  ) {
    const { instructions, logs, signature, innerInstructions } =
      parsedTransactionData;
    const isFoundCreatedPair = logs?.some((log: string | string[]) => {
      return log === PUMPFUN_CREATE_LOG_MESSAGE;
    });

    if (!isFoundCreatedPair) {
      return;
    }
    const createPairInstructions = instructions.filter(
      (instruction) =>
        instruction.programId === PUMPFUN_PROGRAM_ID &&
        instruction.accounts.length === 14,
    );

    const decimals = _.chain(innerInstructions)
      .flatMap('instructions') // Flatten all instructions
      .filter((instr) => _.get(instr, 'parsed.type') === 'initializeMint2') // Filter by type
      .map('parsed.info.decimals') // Extract decimals
      .first() // Get the first value
      .value(); // Resolve the Lodash chain

    for (const createPairInstruction of createPairInstructions) {
      const accounts = createPairInstruction.accounts;
      const tokenAddress = accounts[0];
      const bondingCurve = accounts[2];
      const associatedBondingCurve = accounts[3];
      const creator = accounts[7];
      const createPumpFunPairRequestDto = new CreatePumpfunPairRequestDto();

      this.logger.log(
        `Found new Pump Fun Pair [${tokenAddress}], transaction [${signature}]`,
      );

      createPumpFunPairRequestDto.signatureHash = signature;
      createPumpFunPairRequestDto.pairAddress = bondingCurve;
      createPumpFunPairRequestDto.associatedBondingCurve =
        associatedBondingCurve;
      createPumpFunPairRequestDto.creator = creator;

      const dexLabel =
        await this.dexLabelService.findByProgramAddress(PUMPFUN_PROGRAM_ID);

      if (!dexLabel) {
        throw new DexLabelNotFoundException(PUMPFUN_PROGRAM_ID);
      }

      await Promise.all([
        this.newPairQueue.add(PUMP_FUN_NEW_PAIR_TRANSACTION_JOB, {
          signatureHash: createPumpFunPairRequestDto.signatureHash,
          pairAddress: bondingCurve,
          creator: creator,
          baseAddress: tokenAddress,
          quoteAddress: WSOL_ADDRESS,
          dexLabel: dexLabel.label,
          blockTime: new Date(blockTime),
          extra: {
            associatedBondingCurve,
          } as PumpfunExtra,
        } as CreatePairDto),

        this.tokenService.handlePumpFunToken({
          tokenAddress,
          decimals,
          createdAt: new Date(),
          createPairInstruction: createPairInstruction.data,
          creator,
        }),
      ]);
    }
  }

  async handleUpdateMultiplePumpFunPair(pairData: PairData[]) {
    await this.updateMultiplePairQueue.add(PUMP_FUN_UPDATE_PAIR_JOB, pairData);
  }

  async createPumpFunPair(
    createPairDto: CreatePairDto,
  ): Promise<PairDocument | null> {
    const pair = await this.pairService.savePair(createPairDto);

    if (!pair) {
      this.logger.error(
        `[FAILURE] Failed to store pair for token ${createPairDto.baseAddress}/${createPairDto.quoteAddress}`,
      );
      return null;
    }

    const pairDetail = await this.pairService.populatePairDetail(pair);

    if (!pairDetail) {
      this.logger.error(
        `[FAILURE] Failed to generate pairDetail for bondingCurve ${createPairDto.pairAddress}`,
      );
      return pair;
    }

    const result = await this.pairDetailRepository.createPairDetail(pairDetail);

    if (result.status !== 'inserted') {
      this.logger.error(
        `[FAILURE] Failed to store pairDetail for bondingCurve ${createPairDto.pairAddress}`,
      );
    } else {
      this.logger.log(
        `[SUCCESS] Successfully stored pairDetail for bondingCurve ${createPairDto.pairAddress}`,
      );
    }

    const pairDetailTimeframes =
      await this.pairService.populatePairDetailTimeframes(pair.pairAddress);

    if (!pairDetailTimeframes) {
      this.logger.error(
        `[FAILURE] Failed to generate pairDetailTimeframes for bondingCurve ${createPairDto.pairAddress}`,
      );
      return pair;
    }

    for (const pairDetailTimeframe of pairDetailTimeframes) {
      const savedPairDetailTimeframes =
        await this.pairDetailTimeFramesRepository.createPairDetailTimeframes(
          pairDetailTimeframe,
        );

      if (savedPairDetailTimeframes.status !== 'inserted') {
        this.logger.error(
          `[FAILURE] Failed to store pairDetailTimeframes [${pairDetailTimeframe.timeInterval}] for bondingCurve ${createPairDto.pairAddress}`,
        );
      } else {
        this.logger.log(
          `[SUCCESS] Successfully stored pairDetailTimeframes [${pairDetailTimeframe.timeInterval}] for bondingCurve ${createPairDto.pairAddress}`,
        );
      }
    }

    return pair;
  }

  async updateMultiplePumpfunDetailsPair(pairDatas: PairData[]): Promise<void> {
    if (!pairDatas || pairDatas.length === 0) {
      this.logger.verbose(
        `updateMultiplePumpfunDetailsPair:pairAddress is empty, skipping`,
      );
      return;
    }

    await Promise.allSettled(
      pairDatas.map(async (data) => {
        const lock = await this.redisRepository.redlock.acquire(
          [data.pairAddress],
          10000,
        );
        try {
          await this.updatePumpfunDetailPair(data);
          await this.updatePumpfunDetailTimeframes(data.pairAddress);
        } catch (error) {
          this.logger.error(
            `updateMultiplePumpfunDetailsPair:Failed to update pair detail for ${data.pairAddress}: ${error.message}`,
          );
        } finally {
          await lock.release();
        }
      }),
    );
  }

  private async updatePumpfunDetailPair(data: PairData): Promise<void> {
    const { tokenAddress, pairAddress } = data;
    const pairDetail =
      await this.pairDetailRepository.findOneByPairAddress(pairAddress);

    if (!pairDetail) {
      this.logger.verbose(
        `updateMultiplePumpfunDetailsPair:pairDetail not found for pairAddress ${pairAddress}`,
      );
      return;
    }

    const updatedPairDetail = await this.pairService.populateUpdatedPairDetail(
      tokenAddress,
      pairAddress,
    );

    if (!updatedPairDetail) {
      this.logger.verbose(
        `updateMultiplePumpfunDetailsPair:populateUpdatedPairDetail failed for pairAddress ${pairAddress}`,
      );
      return;
    }
    await this.pairDetailRepository.updatePairDetailPumpFun(updatedPairDetail);
  }

  private async updatePumpfunDetailTimeframes(pairAddress: string) {
    const updatedPairDetailTimeframes =
      await this.pairService.populateUpdatedPairDetailTimeframes(pairAddress);
    if (!updatedPairDetailTimeframes || !updatedPairDetailTimeframes.length) {
      this.logger.verbose(
        `updateMultiplePumpfunDetailsPair:updatePumpfunDetailTimeframes failed for pairAddress ${pairAddress}`,
      );
    }

    for (const updatedPairDetailTimeframe of updatedPairDetailTimeframes) {
      await this.pairDetailTimeFramesRepository.updatePairDetailTimeframes(
        updatedPairDetailTimeframe,
      );
    }
  }
}
