import { forwardRef, Module } from '@nestjs/common';
import { PairService } from './service/pair.service';
import { TokenModule } from '../token/token.module';
import { QueueModule } from '../queue/queue.module';
import {
  FlowName,
  PumpFunQueue,
  PumpSwapQueue,
} from '../../common/constant/queue-name';
import { QueueConfig } from '../queue/queue.interface';
import { RedisModule } from '../redis/redis.module';
import { NewPairPumpFunProcessor } from './processor/new-pair-transaction.processor';
import { SolanaModule } from '../solana/solana.module';
import { DefaultJobOptions } from 'bullmq';
import { UpdateMultiplePairProcessor } from './processor/update-multiple-pair.processor';
import { PairController } from './pair.controller';
import { PairDocumentPersistenceModule } from './infrastructure/persistence/document/document-persistence.module';
import { PairTimeSeriesPersistenceModule } from './infrastructure/persistence/time-series/time-series-persistence.module';
import { SwapTransactionModule } from '../swap-transaction/swap-transaction.module';
import { PairPumpSwapService } from './service/pair-pump-swap.service';
import { FlowQueueModule } from '../queue/flow-queue.module';
import { NewPairDetailPumpswapProcessor } from './processor/new-pair-detail-pump-swap-processor';
import { NewPairPumpSwapProcessor } from './processor/new-pair-pump-swap-transaction.processor';
import { UpdateMultiplePairDetailProcessor } from './processor/update-multiple-pump-swap-pair-detail.processor';
import { DexLabelModule } from '../dex-label/dex-label.module';
import { DexModule } from '../dex/dex.module';
import { PairPumpFunService } from './service/pair-pump-fun.service';
import { TokenBalanceService } from '../token-balance/token-balance.service';

@Module({
  imports: [
    PairDocumentPersistenceModule,
    PairTimeSeriesPersistenceModule,
    RedisModule,
    FlowQueueModule.register({
      flows: Object.values(FlowName),
    }),
    QueueModule.register({
      queues: [
        PumpFunQueue.NEW_PAIR,
        PumpFunQueue.UPDATE_MULTIPLE_PAIR,
        PumpSwapQueue.NEW_PUMPSWAP_PAIR,
        PumpSwapQueue.UPDATE_MULTIPLE_PAIR,
      ].map(
        (queueName) =>
          ({
            name: queueName,
            defaultJobOptions: {
              attempts: 5,
              backoff: {
                type: 'exponential',
                delay: 1000,
              },
              removeOnComplete: true,
              removeOnFail: false,
            } as DefaultJobOptions,
          }) as QueueConfig,
      ),
    }),
    TokenModule,
    SolanaModule,
    DexModule,
    DexLabelModule,
    forwardRef(() => SwapTransactionModule),
  ],
  providers: [
    PairService,
    PairPumpSwapService,
    PairPumpFunService,
    NewPairDetailPumpswapProcessor,
    NewPairPumpSwapProcessor,
    UpdateMultiplePairDetailProcessor,
    NewPairPumpFunProcessor,
    UpdateMultiplePairProcessor,
    TokenBalanceService,
  ],
  controllers: [PairController],
  exports: [PairService, PairPumpSwapService, PairPumpFunService],
})
export class PairModule {}
