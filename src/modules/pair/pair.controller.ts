import { Controller, Get, Query } from '@nestjs/common';
import { PairService } from './service/pair.service';
import { PairDto } from './dto/pair.dto';
import { GetRecentPairsDto } from './dto/get-recent-pairs.dto';
import { GetPairByAddressRequestDto } from './dto/get-pair-by-address-request.dto';
import { GetTokenMetricsDto } from './dto/get-token-metrics.dto';
import { TokenMetricsDto } from './dto/token-metrics.dto';
import { TokenBalanceService } from '../token-balance/token-balance.service';

@Controller('pair')
export class PairController {
  constructor(private readonly pairService: PairService,
    private readonly tokenBalenceService: TokenBalanceService,
  ) {}

  @Get()
  async getPairsByAddress(
    @Query() query: GetPairByAddressRequestDto,
  ): Promise<PairDto> {
    return await this.pairService.getPairsByAddress(query.pairAddress);
  }

  @Get('recent')
  async getRecentNewPairs(@Query() query: GetRecentPairsDto) {
    const {
      limit = 50,
      page = 1,
      sortBy,
      sortDirection = 'desc',
      filterBy,
    } = query;

    return this.pairService.getRecentNewPairsWithin24h(
      limit,
      page,
      filterBy,
      sortBy,
      sortDirection,
    );
  }

  @Get('metrics')
  async getTokenMetrics(
    @Query() query: GetTokenMetricsDto,
  ): Promise<TokenMetricsDto> {
    const { pairAddress } = query;

    const [devHoldsPercent, top10HoldersPercent] = await Promise.all([
      this.pairService.getPercentageDevHolding(pairAddress),
      this.tokenBalenceService.getPercentageTop10Holders(pairAddress),
    ]);

    return {
      devHoldsPercent,
      top10HoldersPercent,
      snipersHoldPercent: 0,
      insidersHoldPercent: 0,
      bundlersHoldPercent: 0,
      numProTrader: 0,
      isOG: false,
      isDexPaid: false,
      clustersHoldPercent: 0,
    };
  }


}
