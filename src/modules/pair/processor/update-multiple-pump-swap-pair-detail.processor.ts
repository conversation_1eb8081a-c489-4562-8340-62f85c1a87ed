import { Processor } from '@nestjs/bullmq';
import { PumpSwapQueue } from '../../../common/constant/queue-name';
import { Inject, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { UpdatePumpSwapPairDetailDto } from '../../swap-transaction/dto/update-pump-swap-pair-detail.dto';
import { WorkerHostProcessor } from '../../queue/worker-host.processor';
import { QueueHandleProcessException } from '../../../common/exceptions/queue-handle-process-exception';
import { PairDetailErrorMessage } from '../../../common/constant/errors';
import { PairPumpSwapService } from '../service/pair-pump-swap.service';

@Processor(PumpSwapQueue.UPDATE_MULTIPLE_PAIR, {
  concurrency: 100,
})
@Injectable()
export class UpdateMultiplePairDetailProcessor extends WorkerHostProcessor {
  constructor(
    @Inject()
    private readonly pairService: PairPumpSwapService,
  ) {
    super();
  }

  async handleProcess(job: Job<UpdatePumpSwapPairDetailDto[]>): Promise<any> {
    const begin = performance.now();
    const updatePumpswapPairDetailDtos = job.data;
    try {
      await this.pairService.handleUpdateMultiplePumpSwapPoolDetail(
        updatePumpswapPairDetailDtos,
      );
      await this.pairService.handleUpdatePumpSwapDetailTimeframes(
        updatePumpswapPairDetailDtos,
      );
    } catch (err) {
      this.logger.debug(err);
      throw new QueueHandleProcessException(
        PairDetailErrorMessage.ERROR_QUEUE_UPDATE_PAIR_DETAIL,
      );
    } finally {
      this.logger.verbose(
        `Completed store pools [${updatePumpswapPairDetailDtos.length}], elapsed: ${(performance.now() - begin).toFixed()}ms `,
      );
    }
  }
}
