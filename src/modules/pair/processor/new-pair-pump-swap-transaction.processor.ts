import { Processor } from '@nestjs/bullmq';
import { PumpSwapQueue } from '../../../common/constant/queue-name';
import { Inject, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { PairPumpSwapService } from '../service/pair-pump-swap.service';
import { WorkerHostProcessor } from '../../queue/worker-host.processor';
import { TokenService } from '../../token/token.service';
import { CreatePairDto } from '../dto/create-pair.dto';
import { CreatePumpSwapPairDetailDto } from '../dto/pair-detail/create-pump-swap-pair-detail.dto';

@Processor(PumpSwapQueue.NEW_PUMPSWAP_PAIR, {
  concurrency: 100,
})
@Injectable()
export class NewPairPumpSwapProcessor extends WorkerHostProcessor {
  constructor(
    @Inject(PairPumpSwapService)
    private readonly pairPumpSwapService: PairPumpSwapService,
    @Inject(TokenService)
    private readonly tokenService: TokenService,
  ) {
    super();
  }

  async handleProcess(
    job: Job<CreatePairDto, CreatePumpSwapPairDetailDto, string>,
  ): Promise<any> {
    const newPairDataDto = job.data as CreatePairDto;
    const quoteAddress = newPairDataDto.quoteAddress;
    const promiseResult = await Promise.allSettled([
      this.tokenService.handlePumpSwapToken(
        newPairDataDto.baseAddress,
        quoteAddress,
      ),
      this.pairPumpSwapService.handleCreatePumpSwapPair(newPairDataDto),
    ]);
    if (promiseResult[0].status === 'rejected') {
      throw new Error(promiseResult[0].reason.message);
    }
    if (promiseResult[1].status === 'rejected') {
      throw new Error(promiseResult[1].reason.message);
    }
    return promiseResult[1].value;
  }
}
