import { OnWorkerEvent, Processor } from '@nestjs/bullmq';
import { PumpFunQueue } from '../../../common/constant/queue-name';
import { Inject, Injectable } from '@nestjs/common';
import { WorkerHostProcessor } from '../../queue/worker-host.processor';
import { Job } from 'bullmq';
import { PairDetailErrorMessage } from 'src/common/constant/errors';
import { PairData } from 'src/modules/swap-transaction/dto/swap-transaction.dto';
import { QueueHandleProcessException } from 'src/common/exceptions/queue-handle-process-exception';
import { PairPumpFunService } from '../service/pair-pump-fun.service';

@Processor(PumpFunQueue.UPDATE_MULTIPLE_PAIR, {
  concurrency: 100,
})
@Injectable()
export class UpdateMultiplePairProcessor extends WorkerHostProcessor {
  constructor(
    @Inject()
    private readonly pairPumpFunService: PairPumpFunService,
  ) {
    super();
  }

  async handleProcess(job: Job<PairData[], void, string>): Promise<void> {
    const begin = performance.now();
    const pairData = job.data;
    try {
      return await this.pairPumpFunService.updateMultiplePumpfunDetailsPair(
        pairData,
      );
    } catch (error) {
      this.logger.error(
        `[UpdateMultiplePairProcessor] Job id=${job.id} failed: ${error}`,
      );
      throw new QueueHandleProcessException(
        PairDetailErrorMessage.ERROR_QUEUE_UPDATE_PAIR_DETAIL,
      );
    } finally {
      this.logger.verbose(
        `Completed store pairs [${pairData.length}], elapsed: ${(performance.now() - begin).toFixed()}ms `,
      );
    }
  }

  @OnWorkerEvent('completed')
  onCompleted(_job: Job<string[], void, string>) {}
}
