import { Processor } from '@nestjs/bullmq';
import { PumpSwapQueue } from '../../../common/constant/queue-name';
import { Inject, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { WorkerHostProcessor } from '../../queue/worker-host.processor';
import { CreatePumpSwapPairDetailDto } from '../dto/pair-detail/create-pump-swap-pair-detail.dto';
import { PairPumpSwapDetailDto } from '../dto/pair-detail/pair-detail.dto';
import { QueueHandleProcessException } from '../../../common/exceptions/queue-handle-process-exception';
import { PairErrorMessage } from '../../../common/constant/errors';
import { PairPumpSwapService } from '../service/pair-pump-swap.service';

@Processor(PumpSwapQueue.NEW_PAIR_DETAIL, {
  concurrency: 100,
})
@Injectable()
export class NewPairDetailPumpswapProcessor extends WorkerHostProcessor {
  constructor(
    @Inject(PairPumpSwapService)
    private readonly pairPumpSwapService: PairPumpSwapService,
  ) {
    super();
  }

  async handleProcess(
    job: Job<CreatePumpSwapPairDetailDto, PairPumpSwapDetailDto, string>,
  ): Promise<any> {
    const pairDetailReturnValue = await job.getChildrenValues();
    if (!pairDetailReturnValue) {
      throw new Error('Undefined value from create new pair job');
    }
    const key = Object.keys(pairDetailReturnValue)[0];
    const pairData = pairDetailReturnValue[key] as CreatePumpSwapPairDetailDto;
    try {
      await this.pairPumpSwapService.handleCreatePumpSwapPairDetail(pairData);
      await this.pairPumpSwapService.handleCreatePumpSwapPairDetailTimeFrames(
        pairData,
      );
    } catch (err) {
      throw new QueueHandleProcessException(
        `${PairErrorMessage.ERROR_QUEUE_NEW_PAIR}: ${err.message}`,
      );
    }
  }
}
