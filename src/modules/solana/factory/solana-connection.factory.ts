import { <PERSON><PERSON>rov<PERSON>, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Connection } from '@solana/web3.js';
import { SolanaConfig } from '../../../config/solana.config';

export const solanaConnectionFactory: FactoryProvider<Connection> = {
  provide: 'SolanaConnection',
  useFactory: (configService: ConfigService) => {
    const logger = new Logger('SolanaConnection');
    const solanaConfig = configService.get<SolanaConfig>('solana');
    const connection = new Connection(
      solanaConfig.rpcUrl,
      solanaConfig.connectionConfig,
    );

    return new Proxy(connection, {
      get: function (target, prop) {
        if (typeof target[prop] === 'function') {
          return async function (...args) {
            const start = performance.now();
            try {
              return await Reflect.get(target, prop).apply(target, args);
            } finally {
              logger.debug(
                `Method [${prop.toString()}] [${(performance.now() - start).toFixed(0)}] ms`,
              );
            }
          };
        }
        return Reflect.get(target, prop);
      },
    });
  },

  inject: [ConfigService],
};
