import { Inject, Injectable, Logger } from '@nestjs/common';
import {
  Connection,
  LAMPORTS_PER_SOL,
  PublicKey,
  TokenAccountBalancePair,
  TokenAmount,
} from '@solana/web3.js';
import { RedisRepository } from '../redis/redis.repository';
import { <PERSON>ache<PERSON>ey, RedisPrefix } from '../../common/constant/redis.prefix';
import { USDC_SOL } from '../../common/constant/onchainPubkey';
import { LIQUIDITY_STATE_LAYOUT_V4 } from '../../common/constant/structType';
import BigNumber from 'bignumber.js';
import axios from 'axios';
import { find, get } from 'lodash';
import { ConfigService } from '@nestjs/config';
import { SolanaConfig } from '../../config/solana.config';
import { TokenAccountInfoDto } from '../token/dto/token-account-info.dto';
import { TokenAssetDto } from '../token/dto/token-asset.dto';
import { TOKEN_WSOL } from '../token/dto/token-wsol';
import { Metaplex } from '@metaplex-foundation/js';
import { AccountLayout } from '@solana/spl-token';
import { TokenNotFoundForMintException } from '../../common/exceptions/token-not-found-for-mint-exception';
import { BusinessLogicException } from 'src/common/exceptions/business-logic-exception';
import { SolanaServiceErrorMessage } from 'src/common/constant/errors';

@Injectable()
export class SolanaService {
  private readonly logger = new Logger(SolanaService.name);
  private readonly solanaConfig: SolanaConfig;

  constructor(
    @Inject('SolanaConnection') private readonly connection: Connection,
    @Inject(RedisRepository) private readonly redisRepository: RedisRepository,
    @Inject() configService: ConfigService,
  ) {
    this.solanaConfig = configService.get<SolanaConfig>('solana');
  }

  async getSolPrice() {
    try {
      const solPrice = await this.redisRepository.get(
        RedisPrefix.CACHE,
        CacheKey.SOL_PRICE_USD,
      );
      if (solPrice) {
        return parseFloat(solPrice);
      }
      const account = await this.connection.getAccountInfo(
        new PublicKey(USDC_SOL),
      );
      if (account) {
        const info = LIQUIDITY_STATE_LAYOUT_V4.decode(account.data);
        const baseVault = info.baseVault.toString();
        const quoteVault = info.quoteVault.toString();
        this.logger.debug(
          `Call getBalance, getSolPrice(), wallet [${quoteVault}`,
        );
        const balanceProcess = await Promise.all([
          this.connection.getBalance(new PublicKey(baseVault)),
          this.connection.getTokenAccountBalance(new PublicKey(quoteVault)),
        ]);
        const balance = BigNumber(balanceProcess[0])
          .div(LAMPORTS_PER_SOL)
          .toNumber();
        const tokenAccountBalance = balanceProcess[1];
        const total = Number(tokenAccountBalance.value.uiAmount);
        await this.redisRepository.setWithExpiry(
          RedisPrefix.CACHE,
          CacheKey.SOL_PRICE_USD,
          BigNumber(total).div(balance).toNumber().toString(),
          120,
        );
        return BigNumber(total).div(balance).toNumber();
      }
      return 0;
    } catch (error: any) {
      this.logger.error(`Error in getSolPrice ${JSON.stringify(error.stack)}`);
      return 0;
    }
  }

  async getTokenSupply(tokenAddress: string): Promise<TokenAmount> {
    try {
      const { value } = await this.connection.getTokenSupply(
        new PublicKey(tokenAddress),
      );

      return value;
    } catch (err) {
      this.logger.error(
        `Fail to get Token supply - ${tokenAddress} - ${JSON.stringify(err)}`,
      );
      return { amount: '0', decimals: 0, uiAmount: 0, uiAmountString: '0' };
    }
  }

  async getTokenTopHolders(
    tokenAddress: string,
    maxHolder: number = 20,
  ): Promise<TokenAccountBalancePair[]> {
    try {
      const { value } = await this.connection.getTokenLargestAccounts(
        new PublicKey(tokenAddress),
      );

      return value.sort((a, b) => b.uiAmount - a.uiAmount).slice(0, maxHolder);
    } catch (err) {
      this.logger.error(
        `Fail to get Token - ${tokenAddress} - top 20 largest Accounts: ${JSON.stringify(err)}`,
      );

      return [];
    }
  }

  async getTokenAccountInfo(
    tokenAddress: string,
  ): Promise<TokenAccountInfoDto> {
    try {
      const tokenAccountInfo = await this.redisRepository.get(
        RedisPrefix.CACHE,
        CacheKey.TOKEN_ACCOUNT_INFO,
      );
      if (tokenAccountInfo !== null) {
        return JSON.parse(tokenAccountInfo) as TokenAccountInfoDto;
      }
      this.logger.verbose(`Getting Token Account Info [${tokenAddress}]`);
      const begin = new Date();
      const { data } = await axios.post(
        this.solanaConfig.rpcUrl,
        {
          jsonrpc: '2.0',
          id: '123',
          method: 'getAccountInfo',
          params: [
            tokenAddress,
            {
              encoding: 'jsonParsed',
              commitment: this.solanaConfig.connectionConfig.commitment,
            },
          ],
        },
        {
          timeout: 10 * 1000,
        },
      );
      const end = new Date();
      this.logger.verbose(
        `Completed get Account Info [${tokenAddress}], [${end.getTime() - begin.getTime()}] ms`,
      );
      const info = data.result.value.data.parsed.info;
      await this.redisRepository.setWithExpiry(
        RedisPrefix.CACHE,
        CacheKey.TOKEN_ACCOUNT_INFO,
        JSON.stringify(info),
        2,
      );
      return info as TokenAccountInfoDto;
    } catch (err) {
      this.logger.error(
        `Error to get token account info [${tokenAddress}]: ${err.message}`,
      );
      throw err;
    }
  }

  async getTokenAsset(tokenAddress: string): Promise<TokenAssetDto> {
    if (tokenAddress === TOKEN_WSOL.wsol) {
      return {
        symbol: TOKEN_WSOL.symbol,
        name: TOKEN_WSOL.name,
      } as TokenAssetDto;
    }
    let tokenInfo = await this.getAsset(tokenAddress);
    if (tokenInfo) {
      return tokenInfo;
    }

    tokenInfo = await this.getTokenMetadataFromMetaflex(tokenAddress);
    if (tokenInfo) {
      return tokenInfo;
    }
    try {
      const accountInfo = await this.getTokenAccountInfo(tokenAddress);
      const extensions = accountInfo.extensions;
      const tokenMetadataElement = find(
        extensions,
        (element) => element.extension === 'tokenMetadata',
      );
      const tokenInfo = new TokenAssetDto();
      const symbol = get(tokenMetadataElement, 'state.symbol');
      const name = get(tokenMetadataElement, 'state.name');
      if (!symbol || !name) {
        throw new Error('Invalid token metadata');
      }
      tokenInfo.symbol = symbol;
      tokenInfo.name = name;
      tokenInfo.description = '';
      return tokenInfo;
    } catch (error) {
      this.logger.error(
        `Error getting accountInfo [${tokenAddress}]: ${error.message}`,
      );
      throw error;
    }
  }

  async getAsset(tokenAddress: string) {
    const tokenInfo = new TokenAssetDto();
    try {
      this.logger.verbose(`Getting Token Asset [${tokenAddress}]`);
      const begin = new Date();

      const { data } = await axios.post(this.solanaConfig.rpcUrl, {
        jsonrpc: '2.0',
        id: '123',
        method: 'getAsset',
        params: {
          id: tokenAddress,
        },
      });
      const end = new Date();
      const name = data.result.content.metadata.name;
      const symbol = data.result.content.metadata.symbol;
      const jsonUrl = data.result.content.json_uri;
      const metadataResponse = await axios.get(jsonUrl);
      this.logger.verbose(
        `Completed get Token Asset [${tokenAddress}], metadata [${jsonUrl}], [${end.getTime() - begin.getTime()}] ms`,
      );
      const metadata = metadataResponse.data;
      const description = metadata?.description;
      tokenInfo.name = name;
      tokenInfo.symbol = symbol;
      tokenInfo.description = description;
      this.logger.verbose(
        `Completed get Token Asset [${tokenAddress}], [${end.getTime() - begin.getTime()}] ms`,
      );
      return tokenInfo;
    } catch (err: Error | any) {
      this.logger.verbose(
        `Error getting token asset [${tokenAddress}]: ${err.message}`,
      );
    }
    return null;
  }

  async getTokenMetadataFromMetaflex(tokenAddress: string) {
    const tokenInfo = new TokenAssetDto();
    try {
      this.logger.verbose(
        `Getting Token Asset from Metaflex [${tokenAddress}]`,
      );
      const begin = new Date();
      const metaplex = Metaplex.make(this.connection as any);
      const token = await metaplex
        .nfts()
        .findByMint({ mintAddress: new PublicKey(tokenAddress) });

      tokenInfo.symbol = token.symbol;
      tokenInfo.name = token.name;
      tokenInfo.description = token.json?.description;
      const end = new Date();
      this.logger.verbose(
        `Completed get Token Asset from Metaflex [${tokenAddress}], [${end.getTime() - begin.getTime()}] ms`,
      );
      return tokenInfo;
    } catch (err: Error | any) {
      this.logger.verbose(
        `Error getting token asset from Metaflex [${tokenAddress}]: ${err.message}`,
      );
    }
    return null;
  }

  /**
   * Get owner's token amount
   * @param creator
   * @param tokenAddress
   * @returns
   */
  async getTokenAmountByOwner(
    creator: string,
    tokenAddress: string,
  ): Promise<number> {
    try {
      const walletPublicKey = new PublicKey(creator);
      const mintPublicKey = new PublicKey(tokenAddress);

      const tokenAccounts = await this.connection.getTokenAccountsByOwner(
        walletPublicKey,
        {
          mint: mintPublicKey,
        },
      );

      if (tokenAccounts.value.length === 0) {
        throw new TokenNotFoundForMintException(tokenAddress);
      }

      const buffer = Buffer.from(tokenAccounts.value[0].account.data);
      const decoded = AccountLayout.decode(buffer);

      return new BigNumber(decoded.amount.toString()).toNumber();
    } catch (error) {
      if (error instanceof TokenNotFoundForMintException) {
        throw error;
      }
      this.logger.error(
        `Failed to get token amount for owner ${creator} and token ${tokenAddress}: ${error.message}`,
        error.stack,
      );
      throw new BusinessLogicException(
        SolanaServiceErrorMessage.ERROR_GET_TOKEN_AMOUNT_BY_OWNER,
      );
    }
  }
}
