import { Injectable, Logger } from '@nestjs/common';
import { DexDocumentRepository } from './infrastructure/persistence/document/repositories/dex-document.repository';
import { DexDocument } from './domain/dex-document';
import { NullableType } from '../../common/type/nullable.type';

@Injectable()
export class DexService {
  private readonly logger = new Logger(DexService.name);

  constructor(private readonly dexDocumentRepository: DexDocumentRepository) {}

  async findById(dexId: string): Promise<NullableType<DexDocument>> {
    return this.dexDocumentRepository.findById(dexId);
  }
}
