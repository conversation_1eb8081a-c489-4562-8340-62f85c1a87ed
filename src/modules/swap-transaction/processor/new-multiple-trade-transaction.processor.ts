import { OnWorkerEvent, Processor } from '@nestjs/bullmq';
import { PumpFunQueue } from '../../../common/constant/queue-name';
import { Inject, Injectable } from '@nestjs/common';
import { WorkerHostProcessor } from '../../queue/worker-host.processor';
import { Job } from 'bullmq';
import { SwapTransactionService } from '../services/swap-transaction.service';
import { NewMultipleTradeTrxRequestDto } from '../dto/new-multiple-trade-trx-request.dto';
import { CreateSwapTransactionRequestDto } from '../dto/create-swap-transaction-request.dto';
import { PairData } from '../dto/swap-transaction.dto';
import { PairPumpFunService } from '../../pair/service/pair-pump-fun.service';

@Processor(PumpFunQueue.NEW_MULTIPLE_TRADE_TRANSACTION, {
  concurrency: 100,
})
@Injectable()
export class NewMultipleTradeTransactionProcessor extends WorkerHostProcessor {
  private begin: number;

  constructor(
    @Inject()
    private readonly swapTransactionService: SwapTransactionService,
    @Inject()
    private readonly pairPumpFunService: PairPumpFunService,
  ) {
    super();
  }

  async handleProcess(
    job: Job<NewMultipleTradeTrxRequestDto>,
  ): Promise<CreateSwapTransactionRequestDto[]> {
    this.begin = performance.now();

    const { slot, tradeEventData } = job.data;
    if (!tradeEventData || tradeEventData.length === 0) {
      return [];
    }
    return await this.swapTransactionService.handleMultipleTradeEventPumpfun(
      tradeEventData,
      slot,
    );
  }

  @OnWorkerEvent('completed')
  async onCompleted(
    job: Job<NewMultipleTradeTrxRequestDto, CreateSwapTransactionRequestDto[]>,
  ) {
    const { id, queueName, data, returnvalue } = job;

    this.logger.verbose(
      `Job id: ${id}, queue: ${queueName}, slot: ${data.slot}, elapsed: ${(performance.now() - this.begin).toFixed(0)}ms`,
    );

    try {
      const pairData: PairData[] = returnvalue.map((tx) => {
        return {
          tokenAddress: tx.tokenAddress,
          pairAddress: tx.pairAddress,
        };
      });
      await this.pairPumpFunService.handleUpdateMultiplePumpFunPair(pairData);
    } catch (error) {
      this.logger.error(
        `Failed to update pairs: ${error.message}, tokens: ${returnvalue
          .map((tx) => tx.tokenAddress)
          .join(', ')}`,
      );
    }
  }
}
