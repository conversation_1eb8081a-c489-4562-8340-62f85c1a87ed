import { OnWorkerEvent, Processor } from '@nestjs/bullmq';
import { Inject, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { reverse, uniqBy } from 'lodash';
import { PumpSwapQueue } from '../../../common/constant/queue-name';
import { SwapTransactionPumpSwapService } from '../services/swap-transaction-pump-swap.service';
import { NewMultiplePumpSwapTradeTrxRequestDto } from '../dto/pump-swap-new-multiple-trade-trx-request.dto';
import { WorkerHostProcessor } from '../../queue/worker-host.processor';
import { TokenService } from '../../token/token.service';
import { PairPumpSwapService } from '../../pair/service/pair-pump-swap.service';

@Processor(PumpSwapQueue.NEW_MULTIPLE_TRADE_TRANSACTION, {
  concurrency: 10,
})
@Injectable()
export class NewMultiplePumpSwapTradeTransactionProcessor extends WorkerHostProcessor {
  constructor(
    @Inject()
    private readonly swapTransactionService: SwapTransactionPumpSwapService,
    @Inject()
    private readonly pairPumpSwapService: PairPumpSwapService,
    @Inject()
    private readonly tokenService: TokenService,
  ) {
    super();
  }

  async handleProcess(
    job: Job<NewMultiplePumpSwapTradeTrxRequestDto, string[], string>,
  ): Promise<string[]> {
    const { id, queueName, data } = job;
    const begin = performance.now();
    const request = data;
    const tradeEventData = request.tradeEventData;
    const slot = request.slot;
    const blockTime = request.blockTime;

    const updatePumpSwapPairDetailDtos =
      await this.swapTransactionService.handleCreateMultiplePumpSwapSwapTransaction(
        tradeEventData,
        slot,
        blockTime,
      );
    // Update last swap trade transaction for each unique pair
    const uniqPumpSwapPairDetailDtos = uniqBy(
      reverse([...updatePumpSwapPairDetailDtos]),
      'pairAddress',
    ).reverse();

    await this.pairPumpSwapService.addUpdatePumpSwapPairDetailDtoToQueue(
      uniqPumpSwapPairDetailDtos,
    );
    this.logger.verbose(
      `Job id: ${id}, completed queue ${queueName} on slot [${slot}], elapsed time [${(performance.now() - begin).toFixed(0)}]ms`,
    );
    return uniqPumpSwapPairDetailDtos.map((dto) => dto.baseMint);
  }

  @OnWorkerEvent('completed')
  async onCompleted(
    job: Job<NewMultiplePumpSwapTradeTrxRequestDto, string[], string>,
  ) {
    const { id, queueName, data, returnvalue } = job;
    try {
      const begin = performance.now();
      for (const updatedToken of returnvalue) {
        await this.tokenService.handlePumpSwapToken(updatedToken);
      }
      this.logger.verbose(
        `Job id: ${id}, completed queue ${queueName} on slot [${data.slot}], elapsed time [${(performance.now() - begin).toFixed(0)}]ms`,
      );
    } catch (error) {
      this.logger.error(
        `Pairs [${returnvalue.join(',')}] Fail to update ${error.message}`,
      );
    }
  }
}
