import { Injectable, Logger } from '@nestjs/common';
import { QuestdbPgService } from 'src/modules/questdb/questdb-pg.service';
import { TxnsCount } from '../../../../dto/txns-count.dto';
import {
  buildBuySellCountsQuery,
  buildBuySellVolumeQuery,
  buildGetAllPairBuyerAndSellerByPairAddress,
  buildGetPairDetailsTimeframesAllByAddressAndTimeframes,
  buildVolume24hQuery,
} from '../../../../../questdb/questdb.query-builder';
import { TradeType } from 'src/common/constant/trade-type';
import { VolumeResult } from '../../../../dto/volume-result.dto';
import { TimeInterval } from '../../../../../pair/domain/pair-detail-timeframes';
import { BuyerSellerResult } from '../../../../dto/buyer-seller-result.dto';
import { PairDetailMetricsDto } from '../../../../dto/pair-detail-metrics.dto';

@Injectable()
export class SwapTransactionRepository {
  private readonly logger = new Logger(SwapTransactionRepository.name);

  constructor(private readonly questdbPgService: QuestdbPgService) {}

  async findBuySellCountsTxnsByPairAddress(
    pairAddress: string,
  ): Promise<TxnsCount> {
    const query = buildBuySellCountsQuery(pairAddress);

    const results = await this.questdbPgService.query(query);

    let buyTotalTxns = 0;
    let sellTotalTxns = 0;

    for (const row of results) {
      if (row.trade_type === TradeType.BUY) {
        buyTotalTxns = Number(row.total_count);
      } else if (row.trade_type === TradeType.SELL) {
        sellTotalTxns = Number(row.total_count);
      }
    }

    return { buyTotalTxns, sellTotalTxns };
  }

  async findBuySellVolumeByPairAddress(
    pairAddress: string,
  ): Promise<VolumeResult> {
    const query = buildBuySellVolumeQuery(pairAddress);

    const results = await this.questdbPgService.query(query);

    let buyTotalVolume = 0;
    let sellTotalVolume = 0;

    for (const row of results) {
      if (row.trade_type === TradeType.BUY) {
        buyTotalVolume = Number(row.total_volume);
      } else if (row.trade_type === TradeType.SELL) {
        sellTotalVolume = Number(row.total_volume);
      }
    }

    return { buyTotalVolume, sellTotalVolume };
  }

  async findVol24hByPairAddress(pairAddress: string): Promise<number> {
    const query = buildVolume24hQuery(pairAddress);

    const result = await this.questdbPgService.query(query);

    const volume = result?.[0]?.volume24h;
    return volume !== null && volume !== undefined ? Number(volume) : 0;
  }

  async findPairDetailsTimeframesAllByAddressAndTimeframes(
    pairAddress: string,
    timeInterval: TimeInterval,
  ): Promise<PairDetailMetricsDto | null> {
    const sql = buildGetPairDetailsTimeframesAllByAddressAndTimeframes(
      pairAddress,
      timeInterval,
    );

    const rawResult = await this.questdbPgService.query(sql);

    if (!rawResult?.length) return null;

    const pairDetailsTimeframes = rawResult[0];

    return {
      pairAddress: pairDetailsTimeframes.pair_address,
      buyVolumeUsd: parseFloat(pairDetailsTimeframes.buy_volume_usd ?? '0'),
      sellVolumeUsd: parseFloat(pairDetailsTimeframes.sell_volume_usd ?? '0'),
      numOfBuyTxs: parseInt(pairDetailsTimeframes.num_of_buy_txs ?? '0'),
      numOfSellTxs: parseInt(pairDetailsTimeframes.num_of_sell_txs ?? '0'),
      numOfBuyers: parseInt(pairDetailsTimeframes.num_of_buyers ?? '0'),
      numOfSellers: parseInt(pairDetailsTimeframes.num_of_sellers ?? '0'),
    };
  }

  async findAllPairBuyerAndSellerByPairAddress(
    pairAddress: string,
  ): Promise<BuyerSellerResult | null> {
    const sql = buildGetAllPairBuyerAndSellerByPairAddress(pairAddress);

    const rawResult = await this.questdbPgService.query(sql);

    if (!rawResult?.length)
      return {
        numOfBuyers: 0,
        numOfSellers: 0,
      };

    const result = rawResult[0];

    return {
      numOfBuyers: parseInt(result.num_of_buyers ?? '0'),
      numOfSellers: parseInt(result.num_of_sellers ?? '0'),
    };
  }
}
