import { Injectable } from '@nestjs/common';
import { QuestdbPgService } from '../../../../../questdb/questdb-pg.service';
import {
  buildGetOHCLSolQuery,
  buildGetOHCLUsdQuery,
} from '../../../../../questdb/questdb.query-builder';
import { SwapTransactionData } from '../../../../interfaces/swap-transaction-data.interface';
import { ChartType } from '../../../../enums/chart-type.enum';
import { UnsupportedChartTypeException } from 'src/common/exceptions/unsupported-chart-type-exception';

@Injectable()
export class CandlestickRepository {
  constructor(private readonly questdbPgService: QuestdbPgService) {}

  async getTransactionData(
    pairAddress: string,
    startTime: number,
    endTime: number,
    interval: number,
    chartType: ChartType,
    page: number,
    limit: number,
  ) {
    let query: { text: string; values: (string | number)[] };
    switch (chartType) {
      case ChartType.SOL:
        query = buildGetOHCLSolQuery(
          pairAddress,
          startTime * 1000000,
          endTime * 1000000,
          `${interval}s`,
          page,
          limit,
        );
        break;
      case ChartType.USD:
        query = buildGetOHCLUsdQuery(
          pairAddress,
          startTime * 1000000,
          endTime * 1000000,
          `${interval}s`,
          page,
          limit,
        );
        break;
      default:
        throw new UnsupportedChartTypeException(
          chartType,
          Object.values(ChartType),
        );
    }
    return await this.questdbPgService.query<SwapTransactionData[]>(query);
  }
}
