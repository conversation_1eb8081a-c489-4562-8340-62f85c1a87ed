import { Inject, Injectable, Logger } from '@nestjs/common';
import { TransactionData } from '../../../common/type/transaction.data';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EventParser, Idl } from '@coral-xyz/anchor';
import PumpSwapIDL from '../../../common/idl/pumpswap.json';
import { PublicKey } from '@solana/web3.js';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { PumpSwapQueue } from '../../../common/constant/queue-name';
import { PUMP_SWAP_NEW_TRADE_TRANSACTION_JOB } from '../../../common/constant/job-name';
import { PumpSwapTradeEventDataDto } from '../dto/pump-swap-trade-event-data.dto';
import { TradeType } from '../../../common/constant/trade-type';
import { CreatePumpSwapSwapTransactionRequestDto } from '../dto/create-pumpswap-swap-transaction-request.dto';
import { SwapTransactionPumpSwap } from '../dto/swap-transaction-pump-swap';
import { UpdatePumpSwapPairDetailDto } from '../dto/update-pump-swap-pair-detail.dto';
import {
  PUMPSWAP_PROGRAM_ID,
  PUMPSWAP_TRADE_LOG_MESSAGE,
} from '../../../common/constant/pumpswap/pumpswap';
import { SolanaService } from '../../solana/solana.service';
import BigNumber from 'bignumber.js';
import base58 from 'bs58';
import {
  PUMP_SWAP_POOL_BUY_LAYOUT,
  PUMP_SWAP_POOL_SELL_LAYOUT,
} from '../../../common/layout/pump-swap-layout';
import { Instruction } from '../../../common/type/instruction';
import { TOKEN_WSOL } from '../../token/dto/token-wsol';
import { SwapTransaction } from '../domain/swap-transaction';
import { QuestdbSenderService } from '../../questdb/questdb-sender.service';
import { SwapTransactionRepository } from '../infrastructure/persistence/time-series/repositories/swap-transaction.repository';
import { uniqBy } from 'lodash';

@Injectable()
export class SwapTransactionPumpSwapService {
  private readonly logger = new Logger(SwapTransactionPumpSwapService.name);

  constructor(
    @InjectQueue(PumpSwapQueue.NEW_MULTIPLE_TRADE_TRANSACTION)
    private readonly multiplePumpSwapQueue: Queue,
    @Inject()
    private readonly swapTransactionRepository: SwapTransactionRepository,
    private readonly questdbSenderService: QuestdbSenderService,
    @Inject() private readonly solanaService: SolanaService,
  ) {}

  async handleMultipleSwapPumpSwap(
    parsedTransactions: TransactionData[],
    slot: number,
    blockTime: number,
  ) {
    const tradeEventDataDtos: PumpSwapTradeEventDataDto[] = [];
    for (const parsedTransactionData of parsedTransactions) {
      const { instructions, logs, signature, innerInstructions } =
        parsedTransactionData;
      const isPumpSwapTx = instructions.some(
        (instruction: { programId: string }) =>
          instruction.programId === PUMPSWAP_PROGRAM_ID,
      );
      const isSwapPumpSwapTx = logs?.some((log: string) => {
        return PUMPSWAP_TRADE_LOG_MESSAGE.includes(log);
      });
      const isPumpSwapTxFromInnerInstruction = innerInstructions.some(
        (innerInstructions: { instructions: Instruction[] }) => {
          return innerInstructions.instructions.some((innerInstruction) => {
            return (
              innerInstruction.programId === PUMPSWAP_PROGRAM_ID &&
              innerInstruction.accounts.length >= 17
            );
          });
        },
      );
      if (!isPumpSwapTx && !isPumpSwapTxFromInnerInstruction) {
        continue;
      }

      if (!isSwapPumpSwapTx) {
        continue;
      }

      const pumpSwapTxFromInstructions = instructions.flatMap(
        (instruction: Instruction, index) => {
          if (
            instruction.programId === PUMPSWAP_PROGRAM_ID &&
            instruction.accounts.length >= 17
          ) {
            const innerInstructionWithRelatedIndex = innerInstructions
              .flatMap((innerInstruction) => {
                if (innerInstruction.index === index) {
                  return innerInstruction.instructions.filter(
                    (ix, index) =>
                      ix.programId === PUMPSWAP_PROGRAM_ID || index === 0,
                  );
                }
              })
              .filter((innerInstruction) => innerInstruction);
            return { instruction, innerInstructionWithRelatedIndex };
          } else {
            return [];
          }
        },
      );

      const pumpSwapTxFromInnerInstruction = innerInstructions.flatMap(
        (innerInstruction: {
          instructions: Instruction[];
          [key: string]: any;
        }) => {
          const hasPumpSwapInstruction = innerInstruction.instructions.some(
            (ix) =>
              ix.programId === PUMPSWAP_PROGRAM_ID &&
              ix.accounts?.length >= 17 &&
              //TODO CHECK CONDITION: IF TRANSACTION SWAP BOTH DIRECTLY IN INSTRUCTION AND INNER INSTRUCTION
              pumpSwapTxFromInstructions.length < 1,
          );
          return hasPumpSwapInstruction ? [innerInstruction] : [];
        },
      );

      const filteredInnerInstruction = pumpSwapTxFromInnerInstruction.flatMap(
        (innerInstruction) => {
          const arrayInnerInstruction = this.findValidInnerInstruction(
            innerInstruction.instructions,
          );
          return arrayInnerInstruction
            .map((elementInnerInstruction) => {
              if (
                elementInnerInstruction[0] ||
                elementInnerInstruction[1].length > 1
              ) {
                return {
                  instruction: elementInnerInstruction[0],
                  innerInstructionWithRelatedIndex: elementInnerInstruction[1],
                };
              }
            })
            .filter((innerInstruction) => innerInstruction);
        },
      );

      const pumpSwapTxInstructions = pumpSwapTxFromInstructions.concat(
        filteredInnerInstruction.flat(),
      );
      try {
        const coder = new BorshCoder(PumpSwapIDL as Idl);
        const parser = new EventParser(
          new PublicKey(PUMPSWAP_PROGRAM_ID),
          coder,
        );

        const events = parser.parseLogs(logs);
        const eventsArray: any = Array.from(events);

        for (let i = 0; i < pumpSwapTxInstructions.length; i++) {
          if (!eventsArray[i]) {
            const tradeEventDataEncode: string =
              pumpSwapTxInstructions[i].innerInstructionWithRelatedIndex[1]
                .data;
            const tradeEventDataDecodeBuffer = Buffer.from(
              base58.decode(tradeEventDataEncode),
            );
            tradeEventDataDtos.push(
              this.parsePumpSwapInstructionToTradeEvent(
                pumpSwapTxInstructions[i],
                tradeEventDataDecodeBuffer,
                signature,
              ),
            );
            continue;
          }
          switch (eventsArray[i].name) {
            case 'BuyEvent':
              tradeEventDataDtos.push(
                this.parseBuyTradeEvent(
                  pumpSwapTxInstructions[i].instruction,
                  eventsArray[i].data,
                  signature,
                ),
              );
              break;
            case 'SellEvent':
              tradeEventDataDtos.push(
                this.parseSellTradeEvent(
                  pumpSwapTxInstructions[i].instruction,
                  eventsArray[i].data,
                  signature,
                ),
              );
              break;
          }
        }
      } catch (error) {
        this.logger.error(error.message);
        this.logger.debug(error);
      }
    }
    if (tradeEventDataDtos.length < 1) {
      return;
    }
    await this.addNewMultipleSwapToQueue(tradeEventDataDtos, slot, blockTime);
  }

  private parseBuyTradeEvent(
    pumpSwapTxInstructions: any,
    event: any,
    signature: string,
  ) {
    const instructionAccounts = pumpSwapTxInstructions;
    const pumpSwapTradeEventDataDto = new PumpSwapTradeEventDataDto();
    pumpSwapTradeEventDataDto.signature = signature;
    const eventData = event;
    switch (instructionAccounts.accounts[3]) {
      case TOKEN_WSOL.address:
        pumpSwapTradeEventDataDto.tradeType = TradeType.SELL;
        pumpSwapTradeEventDataDto.baseMint = instructionAccounts.accounts[4];
        pumpSwapTradeEventDataDto.quoteMint = instructionAccounts.accounts[3];
        pumpSwapTradeEventDataDto.timestamp = eventData.timestamp.toString();
        pumpSwapTradeEventDataDto.tokenAmount =
          eventData.quote_amount_in_with_lp_fee.toString();
        pumpSwapTradeEventDataDto.solAmount =
          eventData.base_amount_out.toString();
        pumpSwapTradeEventDataDto.userBaseTokenReserves =
          eventData.user_quote_token_reserves.toString();
        pumpSwapTradeEventDataDto.userQuoteTokenReserves =
          eventData.user_base_token_reserves.toString();
        pumpSwapTradeEventDataDto.poolBaseTokenReserves =
          eventData.pool_quote_token_reserves.toString();
        pumpSwapTradeEventDataDto.poolQuoteTokenReserves =
          eventData.pool_base_token_reserves.toString();
        pumpSwapTradeEventDataDto.poolAddress = eventData.pool.toString();
        pumpSwapTradeEventDataDto.user = eventData.user.toString();
        break;
      default:
        pumpSwapTradeEventDataDto.tradeType = TradeType.BUY;
        pumpSwapTradeEventDataDto.baseMint = instructionAccounts.accounts[3];
        pumpSwapTradeEventDataDto.quoteMint = instructionAccounts.accounts[4];
        pumpSwapTradeEventDataDto.timestamp = eventData.timestamp.toString();
        pumpSwapTradeEventDataDto.tokenAmount =
          eventData.base_amount_out.toString();
        pumpSwapTradeEventDataDto.solAmount =
          eventData.quote_amount_in_with_lp_fee.toString();
        pumpSwapTradeEventDataDto.userBaseTokenReserves =
          eventData.user_base_token_reserves.toString();
        pumpSwapTradeEventDataDto.userQuoteTokenReserves =
          eventData.user_quote_token_reserves.toString();
        pumpSwapTradeEventDataDto.poolBaseTokenReserves =
          eventData.pool_base_token_reserves.toString();
        pumpSwapTradeEventDataDto.poolQuoteTokenReserves =
          eventData.pool_quote_token_reserves.toString();
        pumpSwapTradeEventDataDto.poolAddress = eventData.pool.toString();
        pumpSwapTradeEventDataDto.user = eventData.user.toString();
        break;
    }
    return pumpSwapTradeEventDataDto as PumpSwapTradeEventDataDto;
  }

  private parseSellTradeEvent(
    pumpSwapTxInstructions: any,
    event: any,
    signature: string,
  ) {
    const instructionAccounts = pumpSwapTxInstructions;
    const pumpSwapTradeEventDataDto = new PumpSwapTradeEventDataDto();
    const eventData = event;
    pumpSwapTradeEventDataDto.signature = signature;
    switch (instructionAccounts.accounts[3]) {
      case TOKEN_WSOL.address:
        pumpSwapTradeEventDataDto.tradeType = TradeType.BUY;
        pumpSwapTradeEventDataDto.baseMint = instructionAccounts.accounts[4];
        pumpSwapTradeEventDataDto.quoteMint = instructionAccounts.accounts[3];
        pumpSwapTradeEventDataDto.timestamp = eventData.timestamp.toString();
        pumpSwapTradeEventDataDto.tokenAmount =
          eventData.user_quote_amount_out.toString();
        pumpSwapTradeEventDataDto.solAmount =
          eventData.base_amount_in.toString();
        pumpSwapTradeEventDataDto.userBaseTokenReserves =
          eventData.user_quote_token_reserves.toString();
        pumpSwapTradeEventDataDto.userQuoteTokenReserves =
          eventData.user_base_token_reserves.toString();
        pumpSwapTradeEventDataDto.poolBaseTokenReserves =
          eventData.pool_quote_token_reserves.toString();
        pumpSwapTradeEventDataDto.poolQuoteTokenReserves =
          eventData.pool_base_token_reserves.toString();
        pumpSwapTradeEventDataDto.poolAddress = eventData.pool.toString();
        pumpSwapTradeEventDataDto.user = eventData.user.toString();
        break;
      default:
        pumpSwapTradeEventDataDto.tradeType = TradeType.SELL;
        pumpSwapTradeEventDataDto.baseMint = instructionAccounts.accounts[3];
        pumpSwapTradeEventDataDto.quoteMint = instructionAccounts.accounts[4];
        pumpSwapTradeEventDataDto.timestamp = eventData.timestamp.toString();
        pumpSwapTradeEventDataDto.tokenAmount =
          eventData.base_amount_in.toString();
        pumpSwapTradeEventDataDto.solAmount =
          eventData.user_quote_amount_out.toString();
        pumpSwapTradeEventDataDto.userBaseTokenReserves =
          eventData.user_base_token_reserves.toString();
        pumpSwapTradeEventDataDto.userQuoteTokenReserves =
          eventData.user_quote_token_reserves.toString();
        pumpSwapTradeEventDataDto.poolBaseTokenReserves =
          eventData.pool_base_token_reserves.toString();
        pumpSwapTradeEventDataDto.poolQuoteTokenReserves =
          eventData.pool_quote_token_reserves.toString();
        pumpSwapTradeEventDataDto.poolAddress = eventData.pool.toString();
        pumpSwapTradeEventDataDto.user = eventData.user.toString();
        break;
    }
    return pumpSwapTradeEventDataDto as PumpSwapTradeEventDataDto;
  }

  private parsePumpSwapInstructionToTradeEvent(
    pumpSwapTxInstructions: any,
    tradeEventDataDecodeBuffer: Buffer,
    signature: string,
  ) {
    const instructionAccounts = pumpSwapTxInstructions;
    const poolAccounts: any[] = instructionAccounts.instruction.accounts.filter(
      (_accounts, index) => index === 7 || index === 8,
    );
    const innerInstructionAccounts = poolAccounts.includes(
      instructionAccounts.innerInstructionWithRelatedIndex[0].accounts[2],
    );
    let pumpSwapTradeEventDataDto = new PumpSwapTradeEventDataDto();
    const isBuySellTx = innerInstructionAccounts ? 'SELL' : 'BUY';
    switch (isBuySellTx) {
      case 'SELL':
        const tradeSellEventDataDecoded = PUMP_SWAP_POOL_SELL_LAYOUT.decode(
          tradeEventDataDecodeBuffer.slice(16),
        );
        pumpSwapTradeEventDataDto = this.parseSellTradeEvent(
          instructionAccounts.instruction,
          tradeSellEventDataDecoded,
          signature,
        );
        break;
      case 'BUY':
        const tradeBuyEventDataDecoded = PUMP_SWAP_POOL_BUY_LAYOUT.decode(
          tradeEventDataDecodeBuffer.slice(16),
        );
        pumpSwapTradeEventDataDto = this.parseBuyTradeEvent(
          instructionAccounts.instruction,
          tradeBuyEventDataDecoded,
          signature,
        );
        break;
    }
    return pumpSwapTradeEventDataDto;
  }

  async addNewMultipleSwapToQueue(
    tradeEventData: PumpSwapTradeEventDataDto[],
    slot: number,
    blockTime: number,
  ): Promise<void> {
    await this.multiplePumpSwapQueue.add(PUMP_SWAP_NEW_TRADE_TRANSACTION_JOB, {
      tradeEventData: tradeEventData,
      slot: slot,
      blockTime: blockTime,
    });
  }

  async handleCreateMultiplePumpSwapSwapTransaction(
    tradeEventDataDto: PumpSwapTradeEventDataDto[],
    slot: number,
    blockTime: number,
  ) {
    const promise = await Promise.allSettled([
      tradeEventDataDto.map((tradeEventDataDto) => {
        const createSwapTransactionRequest =
          new CreatePumpSwapSwapTransactionRequestDto();
        createSwapTransactionRequest.signature = tradeEventDataDto.signature;
        createSwapTransactionRequest.tradeType = tradeEventDataDto.tradeType;
        createSwapTransactionRequest.mint = tradeEventDataDto.baseMint;
        createSwapTransactionRequest.pool = tradeEventDataDto.poolAddress;
        createSwapTransactionRequest.solAmount = Number(
          tradeEventDataDto.solAmount,
        );
        createSwapTransactionRequest.tokenAmount = Number(
          tradeEventDataDto.tokenAmount,
        );
        createSwapTransactionRequest.tradeType = tradeEventDataDto.tradeType;
        createSwapTransactionRequest.user = tradeEventDataDto.user;
        createSwapTransactionRequest.timestamp = new Date(blockTime);
        createSwapTransactionRequest.signature = tradeEventDataDto.signature;
        createSwapTransactionRequest.userBaseTokenReserves =
          tradeEventDataDto.userBaseTokenReserves;
        createSwapTransactionRequest.userQuoteTokenReserves =
          tradeEventDataDto.userQuoteTokenReserves;
        createSwapTransactionRequest.poolBaseTokenReserves =
          tradeEventDataDto.poolBaseTokenReserves;
        createSwapTransactionRequest.poolQuoteTokenReserves =
          tradeEventDataDto.poolQuoteTokenReserves;
        return createSwapTransactionRequest;
      }),
    ]);
    const createSwapTransactionRequests = promise
      .filter((result) => result.status === 'fulfilled')
      .flatMap((result) => result.value);
    await this.createMultiplePumpSwapSwapTransaction(
      createSwapTransactionRequests,
      slot,
    );
    return this.populateUpdatePumpSwapPairDetailDto(
      createSwapTransactionRequests,
    );
  }

  private async createMultiplePumpSwapSwapTransaction(
    createSwapTransactionRequests: CreatePumpSwapSwapTransactionRequestDto[],
    slot: number,
  ) {
    try {
      const swapTransactions: SwapTransactionPumpSwap[] = [];
      for (const createSwapTransactionRequest of createSwapTransactionRequests) {
        const swapTransaction = new SwapTransactionPumpSwap();
        swapTransaction.signature = createSwapTransactionRequest.signature;
        swapTransaction.pairAddress = createSwapTransactionRequest.pool;
        swapTransaction.mint = createSwapTransactionRequest.mint;
        swapTransaction.solAmount = createSwapTransactionRequest.solAmount;
        swapTransaction.tokenAmount = createSwapTransactionRequest.tokenAmount;
        swapTransaction.tradeType = createSwapTransactionRequest.tradeType;
        swapTransaction.tradeUser = createSwapTransactionRequest.user;
        swapTransaction.timestamp = createSwapTransactionRequest.timestamp;
        swapTransaction.userBaseTokenReserves =
          createSwapTransactionRequest.userBaseTokenReserves;
        swapTransaction.userQuoteTokenReserves =
          createSwapTransactionRequest.userQuoteTokenReserves;
        swapTransaction.poolBaseTokenReserves =
          createSwapTransactionRequest.poolBaseTokenReserves;
        swapTransaction.poolQuoteTokenReserves =
          createSwapTransactionRequest.poolQuoteTokenReserves;

        const poolBaseTokenReserves = BigNumber(
          swapTransaction.poolBaseTokenReserves,
        );
        const poolQuoteTokenReserves = BigNumber(
          swapTransaction.poolQuoteTokenReserves,
        );
        let solPrice = new BigNumber(0);
        if (!poolQuoteTokenReserves.isEqualTo(0)) {
          solPrice = poolQuoteTokenReserves.dividedBy(poolBaseTokenReserves);
        }
        const solPriceUsd = await this.solanaService.getSolPrice();
        swapTransaction.solPrice = Number(solPrice.toFixed(9));
        swapTransaction.solPriceUsd = Number(solPriceUsd.toFixed(9));
        swapTransaction.usdPrice = Number(
          solPrice.multipliedBy(solPriceUsd).toFixed(9),
        );
        const swapTx: SwapTransaction = {
          tokenAddress: swapTransaction.mint,
          solAmount: swapTransaction.solAmount,
          tokenAmount: swapTransaction.tokenAmount,
          userPublicKey: swapTransaction.tradeUser,
          tradeType: swapTransaction.tradeType,
          signature: swapTransaction.signature,
          pairPriceSol: swapTransaction.solPrice,
          solPriceUsd: swapTransaction.solPriceUsd,
          timestamp: Number(swapTransaction.timestamp),
          pairAddress: swapTransaction.pairAddress,
          block: Number(slot),
        };
        await this.questdbSenderService.createSwapTransaction(swapTx);
        swapTransactions.push(swapTransaction);
      }
      await this.questdbSenderService.flush();
      this.logger.log(
        `[INFO] Completed storing Pump.swap transactions: ${swapTransactions.length} success, signature[${uniqBy(
          createSwapTransactionRequests,
          'pairAddress',
        )
          .map((swpTx) => swpTx.signature)
          .join(', ')}]`,
      );
      return swapTransactions;
    } catch (error) {
      this.logger.error(
        `[FAILED] Failed to store Pump.swap transaction: signature[${uniqBy(
          createSwapTransactionRequests,
          'pairAddress',
        )
          .map((swpTx) => swpTx.signature)
          .join(', ')}]
           with error: ${error.message}`,
      );
      this.logger.debug(error);
    }
  }

  private populateUpdatePumpSwapPairDetailDto(
    createSwapTransactionRequests: CreatePumpSwapSwapTransactionRequestDto[],
  ) {
    const updatePumpswapPairDetailDtos: UpdatePumpSwapPairDetailDto[] = [];
    for (const swapTransaction of createSwapTransactionRequests) {
      const newUpdatePumpSwapPairDetailDtos: UpdatePumpSwapPairDetailDto =
        new UpdatePumpSwapPairDetailDto();
      newUpdatePumpSwapPairDetailDtos.pairAddress = swapTransaction.pool;
      newUpdatePumpSwapPairDetailDtos.tokenAmount =
        swapTransaction.tokenAmount.toString();
      newUpdatePumpSwapPairDetailDtos.tradeType = swapTransaction.tradeType;
      newUpdatePumpSwapPairDetailDtos.baseMint = swapTransaction.mint;
      newUpdatePumpSwapPairDetailDtos.tokenAmount =
        swapTransaction.tokenAmount.toString();
      newUpdatePumpSwapPairDetailDtos.solAmount =
        swapTransaction.solAmount.toString();
      newUpdatePumpSwapPairDetailDtos.poolBaseTokenReserves =
        swapTransaction.poolBaseTokenReserves;
      newUpdatePumpSwapPairDetailDtos.poolQuoteTokenReserves =
        swapTransaction.poolQuoteTokenReserves;
      updatePumpswapPairDetailDtos.push(newUpdatePumpSwapPairDetailDtos);
    }
    return updatePumpswapPairDetailDtos;
  }

  private findValidInnerInstruction(instructions: Instruction[]) {
    const listInstruction = [];
    let windows = [];
    let relatedInnerInstructionList = [];
    for (let i = 0; i < instructions.length; i++) {
      if (
        instructions[i].programId === PUMPSWAP_PROGRAM_ID &&
        instructions[i].accounts &&
        instructions[i].accounts.length >= 19
      ) {
        windows = [];
        relatedInnerInstructionList = [];
        relatedInnerInstructionList.push(instructions[i + 1]);
        windows.push(instructions[i]);
        for (let j = i + 1; j < instructions.length; j++) {
          if (
            instructions[j].programId === PUMPSWAP_PROGRAM_ID &&
            instructions[j].accounts &&
            instructions[j].accounts.length === 1
          ) {
            relatedInnerInstructionList.push(instructions[j]);
            i = j;
            break;
          }
        }
      }
      windows.push(relatedInnerInstructionList);
      listInstruction.push(windows);
    }

    return listInstruction;
  }

  async getPumpSwapTokenBuySellForVolumeAndTxns(pairAddress: string) {
    let begin = performance.now();
    const totalTxns =
      await this.swapTransactionRepository.findBuySellCountsTxnsByPairAddress(
        pairAddress,
      );
    this.logger.verbose(
      `getTokenBuySellForVolumeAndTxns:totalTxns [${(performance.now() - begin).toFixed()}]ms`,
    );
    begin = performance.now();
    const totalVolume =
      await this.swapTransactionRepository.findBuySellVolumeByPairAddress(
        pairAddress,
      );
    this.logger.verbose(
      `getTokenBuySellForVolumeAndTxns:totalVolume [${(performance.now() - begin).toFixed()}]ms`,
    );

    return { totalTxns, totalVolume };
  }

  async findVol24hByPairAddress(pairAddress: string): Promise<number> {
    return await this.swapTransactionRepository.findVol24hByPairAddress(
      pairAddress,
    );
  }

  async getAllPairBuyerAndSellerByPairAddress(pairAddress: string) {
    const begin = performance.now();

    const pairBuyerAndSeller =
      await this.swapTransactionRepository.findAllPairBuyerAndSellerByPairAddress(
        pairAddress,
      );
    this.logger.verbose(
      `getAllByAddressAndTimeframes: [${(performance.now() - begin).toFixed()}]ms`,
    );

    return pairBuyerAndSeller;
  }
}
