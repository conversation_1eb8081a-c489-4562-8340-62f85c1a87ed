import { forwardRef, Module } from '@nestjs/common';
import { SwapTransactionService } from './services/swap-transaction.service';
import { QueueModule } from '../queue/queue.module';
import { PumpFunQueue, PumpSwapQueue } from '../../common/constant/queue-name';
import { DefaultJobOptions } from 'bullmq';
import { QueueConfig } from '../queue/queue.interface';
import { RedisModule } from '../redis/redis.module';
import { PairModule } from '../pair/pair.module';
import { SolanaModule } from '../solana/solana.module';
import { QuestdbModule } from '../questdb/questdb.module';
import { NewMultipleTradeTransactionProcessor } from './processor/new-multiple-trade-transaction.processor';
import { QuestdbSenderService } from '../questdb/questdb-sender.service';
import { SwapTransactionTimeSeriesPersistenceModule } from './infrastructure/persistence/time-series/time-series-persistence.module';
import { SwapTransactionPumpSwapService } from './services/swap-transaction-pump-swap.service';
import { NewMultiplePumpSwapTradeTransactionProcessor } from './processor/new-multiple-pump-swap-trade-transaction.processor';
import { TokenModule } from '../token/token.module';
import { CandlestickService } from './services/candlestick.service';
import { CandlestickController } from './candlestick.controller';
import { CandlestickRepository } from './infrastructure/persistence/time-series/repositories/candlestick.repository';

@Module({
  imports: [
    SwapTransactionTimeSeriesPersistenceModule,
    QueueModule.register({
      queues: [
        PumpFunQueue.NEW_MULTIPLE_TRADE_TRANSACTION,
        PumpSwapQueue.NEW_MULTIPLE_TRADE_TRANSACTION,
      ].map(
        (queueName) =>
          ({
            name: queueName,
            defaultJobOptions: {
              attempts: 5,
              backoff: {
                type: 'exponential',
                delay: 1000,
              },
              removeOnComplete: true,
              removeOnFail: false,
            } as DefaultJobOptions,
          }) as QueueConfig,
      ),
    }),
    RedisModule,
    forwardRef(() => PairModule),
    forwardRef(() => TokenModule),
    SolanaModule,
    QuestdbModule,
  ],
  providers: [
    SwapTransactionService,
    SwapTransactionPumpSwapService,
    NewMultipleTradeTransactionProcessor,
    NewMultiplePumpSwapTradeTransactionProcessor,
    QuestdbSenderService,
    CandlestickService,
    CandlestickRepository,
  ],
  exports: [SwapTransactionService, SwapTransactionPumpSwapService],
  controllers: [CandlestickController],
})
export class SwapTransactionModule {}
