import { TransactionData } from '../../../common/type/transaction.data';
import {
  TokenBalance,
  UnixTimestamp,
} from '@triton-one/yellowstone-grpc/dist/types/grpc/solana-storage';

export type TransactionInfo = {
  signature: string;
  transaction: TransactionData;
};

export type KafkaMessage = {
  slot: string;
  transactionsInfo: TransactionInfo[];
  blockCreatedTime: number;
};

export type PostBalanceInfo = {
  signature: string;
  postTokenBalances: TokenBalance[];
  index: string;
};

export type PostBalanceMessage = {
  slot: number;
  blockTime: UnixTimestamp | undefined;
  postBalanceInfos: PostBalanceInfo[];
};
