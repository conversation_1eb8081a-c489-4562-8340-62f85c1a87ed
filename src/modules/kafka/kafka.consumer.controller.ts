import { Controller, Inject, Logger } from '@nestjs/common';
import { KafkaTopic } from '../../common/constant/kafka';
import { EventPattern } from '@nestjs/microservices';
import { PumpfunHandler } from './handlers/pumpfun.handler';
import { PumpswapHandler } from './handlers/pumpswap.handler';
import { SplTokenHandler } from './handlers/spl-token.handler';
import { PostBalanceMessage } from './types/kafka.type';

@Controller()
export class KafkaConsumerController {
  private readonly logger = new Logger(KafkaConsumerController.name);

  constructor(
    @Inject(PumpswapHandler)
    private readonly pumpSwapHandler: PumpswapHandler,
    @Inject(PumpfunHandler)
    private readonly pumpFunHandler: PumpfunHandler,
    @Inject(SplTokenHandler)
    private readonly splTokenHandler: SplTokenHandler,
  ) {}

  @EventPattern(KafkaTopic.PUMP_SWAP)
  async handleEventPumpSwap(messages: any) {
    try {
      await this.pumpSwapHandler.handlePumpSwapTransactions(messages);
    } catch (error) {
      this.logger.warn(
        `Failed to handle Kafka messages for PumpSwap, ${error.message}`,
      );
      this.logger.error(error.stack);
    }
  }

  @EventPattern(KafkaTopic.PUMP_FUN)
  async handleEventPumpFun(messages: any) {
    try {
      if (!messages) return;
      await this.pumpFunHandler.handlePumpFunTransactions(messages);
    } catch (error) {
      this.logger.warn(`Fail to handle data from grpc, ${error.message}`);
      this.logger.error(error.stack);
    }
  }

  @EventPattern(KafkaTopic.SPL_TOKEN)
  async handleEventSplToken(messages: PostBalanceMessage) {
    try {
      if (!messages) return;

      await this.splTokenHandler.handleSplTokenTransactions(messages);
    } catch (error) {
      this.logger.warn(`Fail to handle data from grpc, ${error.message}`);
      this.logger.error(error.stack);
    }
  }
}
