import { Inject, Injectable, Logger } from '@nestjs/common';
import { SwapTransactionService } from 'src/modules/swap-transaction/services/swap-transaction.service';
import { KafkaMessage } from '../types/kafka.type';
import { TransactionData } from 'src/common/type/transaction.data';
import { parsePumpfunTransaction } from '../utils/solana-parser.util';
import { PairPumpFunService } from '../../pair/service/pair-pump-fun.service';

@Injectable()
export class PumpfunHandler {
  private readonly logger = new Logger(PumpfunHandler.name);

  constructor(
    @Inject(SwapTransactionService)
    private readonly swapTransactionPumpfunService: SwapTransactionService,
    @Inject(PairPumpFunService)
    private readonly pairService: PairPumpFunService,
  ) {}

  async handlePumpFunTransactions(data: KafkaMessage) {
    const { slot, transactionsInfo = [], blockCreatedTime } = data;

    const transactions = transactionsInfo.map(
      (transactionInfo) => transactionInfo.transaction,
    );

    this.logger.verbose(
      `Handling Pump.fun, slot [${slot}], found transactions [${transactions.length}]`,
    );
    try {
      const parsedTransactions = transactions
        .map((tx: TransactionData) => parsePumpfunTransaction(tx))
        .filter(Boolean) as TransactionData[];
      await Promise.allSettled([
        this.swapTransactionPumpfunService.handleMultipleSwapPumpFun(
          parsedTransactions,
          slot,
        ),
        ...parsedTransactions.map(async (transaction: TransactionData) => {
          await this.pairService.handlePumpFunPair(
            transaction,
            Number(blockCreatedTime) * 1000,
          );
        }),
      ]);
    } catch (error) {
      this.logger.warn(`Fail to handle data from grpc, ${error.message}`);
      this.logger.error(error.stack);
    }
  }
}
