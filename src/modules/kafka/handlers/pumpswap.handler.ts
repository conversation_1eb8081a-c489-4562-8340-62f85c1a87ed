import { Inject, Injectable, Logger } from '@nestjs/common';
import { KafkaMessage } from '../types/kafka.type';
import { parsePumpswapTransaction } from '../utils/solana-parser.util';
import { SwapTransactionPumpSwapService } from '../../swap-transaction/services/swap-transaction-pump-swap.service';
import { PairPumpSwapService } from '../../pair/service/pair-pump-swap.service';
import { TransactionData } from '../../../common/type/transaction.data';

@Injectable()
export class PumpswapHandler {
  private readonly logger = new Logger(PumpswapHandler.name);

  constructor(
    @Inject(SwapTransactionPumpSwapService)
    private readonly swapTransactionPumpswapService: SwapTransactionPumpSwapService,
    @Inject(PairPumpSwapService)
    private readonly poolService: PairPumpSwapService,
  ) {}

  async handlePumpSwapTransactions(data: KafkaMessage) {
    const { slot, transactionsInfo, blockCreatedTime } = data;

    const transactions = transactionsInfo.map(
      (transactionInfo) => transactionInfo.transaction,
    );

    this.logger.verbose(
      `Handling PumpSwap, slot [${slot}], found transactions [${transactions.length}]`,
    );

    try {
      const parsedTransactions = transactions.map(
        (transaction: TransactionData) => parsePumpswapTransaction(transaction),
      );
      await Promise.allSettled([
        this.swapTransactionPumpswapService.handleMultipleSwapPumpSwap(
          parsedTransactions,
          Number(slot),
          Number(blockCreatedTime),
        ),
        ...parsedTransactions.map(async (transaction: TransactionData) => {
          await this.poolService.handlePumpSwapPair(
            transaction,
            Number(blockCreatedTime) * 1000,
          );
        }),
      ]);
    } catch (error) {
      this.logger.warn(
        `Fail to handle PumpSwap data from grpc, ${error.message}`,
      );
      this.logger.error(error.stack);
    }
  }
}
