import { TokenSchemaClass } from '../entities/token.schema';
import { TokenDocument } from '../../../../domain/token-document';

export class TokenDocumentMapper {
  public static toDomain(raw: TokenSchemaClass): TokenDocument {
    const domain = new TokenDocument();

    domain.tokenAddress = raw.tokenAddress;
    domain.tokenName = raw.tokenName;
    domain.symbol = raw.symbol;
    domain.description = raw.description;
    domain.image = raw.image;
    domain.bannerImage = raw.bannerImage;
    domain.decimals = raw.decimals;
    domain.twitter = raw.twitter;
    domain.website = raw.website;
    domain.telegram = raw.telegram;
    domain.createdAt = raw.createdAt;
    domain.updatedAt = raw.updatedAt;
    domain.creator = raw.creator || '';

    return domain;
  }
}
