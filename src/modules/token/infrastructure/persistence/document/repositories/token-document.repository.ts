import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  TokenSchemaClass,
  TokenSchemaDocument,
} from '../entities/token.schema';
import { TokenDocument } from '../../../../domain/token-document';
import { TokenDocumentMapper } from '../mappers/token-document.mapper';
import { TokenNotFoundException } from '../../../../../../common/exceptions/token-not-found-exception';

@Injectable()
export class TokenDocumentRepository {
  private readonly logger = new Logger(TokenDocumentRepository.name);

  constructor(
    @InjectModel(TokenSchemaClass.name)
    private readonly tokenModel: Model<TokenSchemaDocument>,
  ) {}

  async create(data: TokenSchemaClass): Promise<TokenDocument> {
    const createdToken = new this.tokenModel(data);
    const tokenObject = await createdToken.save();
    return TokenDocumentMapper.toDomain(tokenObject);
  }

  async findByTokenAddresses(
    tokenAddresses: string[],
  ): Promise<TokenDocument[]> {
    const tokens = await this.tokenModel
      .find({ tokenAddress: { $in: tokenAddresses } })
      .lean()
      .exec();
    return tokens.map(TokenDocumentMapper.toDomain);
  }

  async findByTokenAddress(
    tokenAddress: string,
  ): Promise<TokenDocument | null> {
    const token = await this.tokenModel.findOne({ tokenAddress }).lean().exec();
    if (!token) {
      throw new TokenNotFoundException(`tokenAddress ${tokenAddress}`);
    }
    return TokenDocumentMapper.toDomain(token);
  }
}
