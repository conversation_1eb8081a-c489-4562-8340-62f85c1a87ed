import { Inject, Injectable, Logger } from '@nestjs/common';
import bs58 from 'bs58';
import { InjectQueue } from '@nestjs/bullmq';
import { PumpFunQueue, PumpSwapQueue } from '../../common/constant/queue-name';
import { Queue } from 'bullmq';
import {
  PUMP_FUN_NEW_TOKEN_TRANSACTION_JOB,
  PUMP_SWAP_NEW_TOKEN_TRANSACTION_JOB,
} from '../../common/constant/job-name';
import { CreateTokenRequestDto } from './dto/create-token-request.dto';
import { TokenDocumentRepository } from './infrastructure/persistence/document/repositories/token-document.repository';
import { TokenSchemaClass } from './infrastructure/persistence/document/entities/token.schema';
import { PUMP_FUN_TOKEN_DATA_LAYOUT } from '../../common/layout/create-pump-fun-token-data';
import { TokenAssetDto } from './dto/token-asset.dto';
import { TokenAccountInfoDto } from './dto/token-account-info.dto';
import { TOKEN_WSOL } from './dto/token-wsol';
import { TokenDocument } from './domain/token-document';
import { SolanaService } from '../solana/solana.service';
import { HandlePumpFunTokenParams } from './dto/handle-pumpfun-token-params.dto';

@Injectable()
export class TokenService {
  private readonly logger: Logger = new Logger(TokenService.name);

  constructor(
    @Inject()
    private readonly tokenRepository: TokenDocumentRepository,
    @InjectQueue(PumpFunQueue.NEW_TOKEN)
    private readonly newTokenQueue: Queue,
    @InjectQueue(PumpSwapQueue.NEW_TOKEN)
    private readonly newPumpSwapTokenQueue: Queue,
    @Inject()
    private readonly solanaService: SolanaService,
  ) {}

  async handlePumpFunToken(params: HandlePumpFunTokenParams) {
    const {
      tokenAddress,
      decimals,
      createdAt,
      createPairInstruction,
      creator,
    } = params;

    try {
      const bufferData = bs58.decode(createPairInstruction);
      // Remove first 8 bytes for event cpi
      const tokenInfo = PUMP_FUN_TOKEN_DATA_LAYOUT.decode(
        Buffer.from(bufferData),
      );

      await this.newTokenQueue.add(PUMP_FUN_NEW_TOKEN_TRANSACTION_JOB, {
        name: tokenInfo.name,
        symbol: tokenInfo.symbol,
        decimals: decimals,
        description: tokenInfo.uri,
        createdAt: createdAt,
        tokenAddress: tokenAddress,
        creator,
      } as CreateTokenRequestDto);
    } catch (err) {
      this.logger.error(
        `Failed to save token for address: ${tokenAddress} with reason: ${err.message}`,
      );
      this.logger.debug(err.stack);
    }
  }

  async createPumpFunToken(
    tokenDto: CreateTokenRequestDto,
  ): Promise<TokenDocument> {
    let tokenDescription = '';
    let tokenImage = '';
    let twitter = '';
    let website = '';
    let telegram = '';

    const { tokenAddress, name, symbol, decimals, creator, createdAt } =
      tokenDto;

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      try {
        const response = await fetch(tokenDto.description, {
          signal: controller.signal,
        });
        clearTimeout(timeoutId);

        if (response.ok) {
          const responseObj = await response.json();

          tokenDescription = responseObj.description ?? '';
          tokenImage = responseObj.image ?? '';
          twitter = responseObj.twitter ?? '';
          website = responseObj.website ?? '';
          telegram = responseObj.telegram ?? '';
        } else {
          this.logger.warn(
            `[WARN] Description URI returned non-OK response: ${response.status} — ${tokenDto.description}`,
          );
        }
      } finally {
        clearTimeout(timeoutId);
      }
    } catch (error) {
      this.logger.warn(
        `[WARN] Failed to fetch token description from ${tokenDto.description}: ${error.message}`,
        error.stack,
      );
    }

    const tokenData: TokenSchemaClass = {
      tokenAddress: tokenAddress,
      tokenName: name,
      symbol: symbol,
      description: tokenDescription,
      image: tokenImage,
      decimals: decimals,
      twitter,
      website,
      telegram,
      bannerImage: '', // Always default on create because it does not exist on chain
      creator,
      createdAt: createdAt,
    };

    try {
      const token = await this.tokenRepository.create(tokenData);
      this.logger.log(`[SUCCESS] Stored token: ${tokenDto.tokenAddress}`);
      return token;
    } catch (err) {
      this.logger.error(
        `[FAILURE] Failed to store token ${tokenDto.tokenAddress}: ${err.message}`,
        err.stack,
      );
      throw err;
    }
  }

  async handlePumpSwapToken(
    baseTokenAddress: string,
    quoteTokenAddress?: string,
  ) {
    if (quoteTokenAddress) {
      if (baseTokenAddress === TOKEN_WSOL.address) {
        baseTokenAddress = quoteTokenAddress;
      }
    }
    const tokenAddress = baseTokenAddress;
    const tokenEntityExist =
      await this.tokenRepository.findByTokenAddress(tokenAddress);
    if (tokenEntityExist) {
      return;
    }
    await this.newPumpSwapTokenQueue.add(
      PUMP_SWAP_NEW_TOKEN_TRANSACTION_JOB,
      tokenAddress,
      {
        deduplication: { id: tokenAddress },
      },
    );
    return tokenAddress;
  }

  async createPumpSwapToken(baseAddress: string) {
    try {
      this.logger.verbose(`Starting to create a new token [${baseAddress}]`);
      const begin = new Date();
      const result = await Promise.all([
        this.solanaService.getTokenAsset(baseAddress),
        this.solanaService.getTokenAccountInfo(baseAddress),
      ]);
      const tokenAsset = result[0];
      const tokenAccountInfo = result[1];
      const tokenInfo = this.populateTokenAssetDtoToTokenDto(tokenAsset);
      this.populateTokenAccountInfoToTokenDto(tokenAccountInfo, tokenInfo);

      const tokenData: TokenSchemaClass = {
        tokenAddress: baseAddress,
        tokenName: tokenInfo.name,
        symbol: tokenInfo.symbol,
        description: tokenInfo.description ?? '',
        image: '',
        decimals: tokenInfo.decimals,
        twitter: '',
        website: '',
        telegram: '',
        bannerImage: '', // Always default on create because it does not exist on chain
        creator: '',
        createdAt: new Date(),
      };
      await this.tokenRepository.create(tokenData);
      const end = new Date();
      this.logger.verbose(
        `Finished creating a new token [${baseAddress}] in ${
          end.getTime() - begin.getTime()
        } ms`,
      );
    } catch (err) {
      this.logger.error(
        `Failed to save token for address: [${baseAddress}] with reason: ${err.message}`,
      );
      this.logger.debug(err.stack);
    }
  }

  async getTokensByAddresses(
    tokenAddresses: string[],
  ): Promise<TokenDocument[]> {
    return await this.tokenRepository.findByTokenAddresses(tokenAddresses);
  }

  async getTokenByAddress(tokenAddresses: string): Promise<TokenDocument> {
    return await this.tokenRepository.findByTokenAddress(tokenAddresses);
  }

  public populateTokenAssetDtoToTokenDto(
    tokenAssetDto: TokenAssetDto,
    tokenDto?: CreateTokenRequestDto,
  ) {
    if (!tokenDto) {
      tokenDto = new CreateTokenRequestDto();
    }
    tokenDto.symbol = tokenAssetDto.symbol;
    tokenDto.name = tokenAssetDto.name;
    if (typeof tokenAssetDto.description !== 'string') {
      tokenAssetDto.description = JSON.stringify(tokenAssetDto.description);
    }
    if (!tokenAssetDto.description) {
      tokenDto.description = '';
    }
    tokenDto.description = tokenAssetDto.description;
    return tokenDto;
  }

  public populateTokenAccountInfoToTokenDto(
    tokenAccountInfo: TokenAccountInfoDto,
    tokenDto?: CreateTokenRequestDto,
  ) {
    tokenDto.decimals = tokenAccountInfo.decimals;
    tokenDto.isRenounced = tokenAccountInfo.mintAuthority === null;
    tokenDto.isFreezeRevoked = tokenAccountInfo.freezeAuthority === null;
    tokenDto.totalSupply = tokenAccountInfo.supply;
    return tokenDto;
  }
}
