import { Module } from '@nestjs/common';
import { TokenService } from './token.service';
import { NewTokenTransactionProcessor } from './processor/new-token-transaction.processor';
import { RedisModule } from '../redis/redis.module';
import { QueueModule } from '../queue/queue.module';
import { PumpFunQueue, PumpSwapQueue } from '../../common/constant/queue-name';
import { QueueConfig } from '../queue/queue.interface';
import { DefaultJobOptions } from 'bullmq';
import { TokenDocumentPersistenceModule } from './infrastructure/persistence/document/token-document-persistence.module';
import { SolanaModule } from '../solana/solana.module';
import { NewPumpSwapTokenTransactionProcessor } from './processor/new-pumpswap-token.processor';
import { TokenController } from './token.controller';

@Module({
  imports: [
    RedisModule,
    QueueModule.register({
      queues: [PumpFunQueue.NEW_TOKEN, PumpSwapQueue.NEW_TOKEN].map(
        (queueName) =>
          ({
            name: queueName,
            defaultJobOptions: {
              attempts: 5,
              backoff: {
                type: 'exponential',
                delay: 1000,
              },
              removeOnComplete: true,
              removeOnFail: false,
            } as DefaultJobOptions,
          }) as QueueConfig,
      ),
    }),
    TokenDocumentPersistenceModule,
    SolanaModule,
  ],
  providers: [
    TokenService,
    NewTokenTransactionProcessor,
    NewPumpSwapTokenTransactionProcessor,
  ],
  controllers: [TokenController],
  exports: [TokenService],
})
export class TokenModule {}
