import { Processor } from '@nestjs/bullmq';
import { PumpSwapQueue } from '../../../common/constant/queue-name';
import { Inject, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { TokenService } from '../token.service';
import { WorkerHostProcessor } from '../../queue/worker-host.processor';

@Processor(PumpSwapQueue.NEW_TOKEN, {
  concurrency: 100,
})
@Injectable()
export class NewPumpSwapTokenTransactionProcessor extends WorkerHostProcessor {
  constructor(
    @Inject()
    private readonly tokenService: TokenService,
  ) {
    super();
  }

  async handleProcess(job: Job<string, string, string>): Promise<any> {
    try {
      const begin = performance.now();
      const tokenAddress = job.data;
      await this.tokenService.createPumpSwapToken(tokenAddress);
      this.logger.log(
        `[SUCCESS] Processed token ${tokenAddress}, elapsed: ${(performance.now() - begin).toFixed()}ms `,
      );
    } catch (error) {
      this.logger.error(
        `${PumpSwapQueue.NEW_TOKEN} handleProcess error: ${error.message}`,
      );
      this.logger.debug(error.stack);
    }
  }
}
