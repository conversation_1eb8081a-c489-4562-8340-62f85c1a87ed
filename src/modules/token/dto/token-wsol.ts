import { TOKEN_PROGRAM_ID } from '@solana/spl-token';

export const TOKEN_WSOL: TokenInfo = {
  chainId: 101,
  address: 'So11111111111111111111111111111111111111112',
  wsol: '7YttLkHDoNj9wyDur5pM1ejNaAvT9X4eqaYcHQqtj2G5',
  programId: TOKEN_PROGRAM_ID.toBase58(),
  decimals: 9,
  symbol: 'WSOL',
  name: 'Wrapped SOL',
  logoURI: `https://img.raydium.io/icon/So11111111111111111111111111111111111111112.png`,
  type: 'raydium',
};

export type TokenInfo = {
  chainId: number;
  address: string;
  wsol: string;
  programId: string;
  decimals: number;
  symbol: string;
  name: string;
  logoURI: string;
  type: string;
};
