import { Injectable, Logger } from '@nestjs/common';
import { QuestdbPgService } from '../../../../../questdb/questdb-pg.service';
import {
  buildGetTopHoldersQuery,
  buildGetTopHoldersTokenAmountQuery,
} from '../../../../../questdb/questdb.query-builder';

@Injectable()
export class TokenBalanceRepository {
  private readonly logger = new Logger(TokenBalanceRepository.name);

  constructor(private readonly questdbPgService: QuestdbPgService) {}

  async findTopHolders(tokenAddress: string) {
    const query = buildGetTopHoldersQuery(tokenAddress);

    return await this.questdbPgService.query(query);
  }
  async findTopHoldersTokenAmount(tokenAddress: string): Promise<number[]> {
    const query = buildGetTopHoldersTokenAmountQuery(tokenAddress);
    const result = await this.questdbPgService.query(query);

    return result.map((tokenAmount) => Number(tokenAmount.token_amount));
  }
}
