import { Module } from '@nestjs/common';
import { QueueName } from '../../common/constant/queue-name';
import { DefaultJobOptions } from 'bullmq';
import { TokenBalanceProcessor } from './processor/token-balance.processor';
import { QuestdbModule } from 'src/modules/questdb/questdb.module';
import { QuestdbSenderService } from 'src/modules/questdb/questdb-sender.service';
import { QueueConfig } from 'src/modules/queue/queue.interface';
import { QueueModule } from 'src/modules/queue/queue.module';
import { TokenBalanceService } from './token-balance.service';
import { TokenBalanceRepository } from './infrastructure/persistence/time-series/repositories/token-balance-repository';

@Module({
  imports: [
    QueueModule.register({
      queues: [QueueName.TOKEN_BALANCE_CHANGE].map(
        (queueName) =>
          ({
            name: queueName,
            defaultJobOptions: {
              attempts: 5,
              backoff: {
                type: 'exponential',
                delay: 1000,
              },
              removeOnComplete: true,
              removeOnFail: false,
            } as DefaultJobOptions,
          }) as QueueConfig,
      ),
    }),
    QuestdbModule,
  ],
  providers: [
    TokenBalanceService,
    TokenBalanceProcessor,
    QuestdbSenderService,
    TokenBalanceRepository,
  ],
  exports: [TokenBalanceService],
})
export class TokenBalanceModule {}
