import { Injectable, Logger } from '@nestjs/common';
import { TokenBalanceChangeDto } from './dto/token-balance-change.dto';
import { InjectQueue } from '@nestjs/bullmq';
import { QueueName } from 'src/common/constant/queue-name';
import { Queue } from 'bullmq';
import { TOKEN_BALANCE_CHANGE_JOB } from 'src/common/constant/job-name';
import { SOL_ADDRESS } from '../../common/constant/onchainPubkey';
import { QuestdbSenderService } from 'src/modules/questdb/questdb-sender.service';
import { ParsedTransactionData } from 'src/modules/kafka/types/spl-token-parsed-transaction-data';
import { GetTopHoldersDto } from './dto/get-top-holders-request.dto';
import { TokenBalanceRepository } from './infrastructure/persistence/time-series/repositories/token-balance-repository';
import { TokenMetricsException } from '../../common/exceptions/token-metrics-exception';
import { PairNotFoundException } from '../../common/exceptions/pair-not-found-exception';
import { TokenNotFoundException } from '../../common/exceptions/token-not-found-exception';
import { PairService } from '../pair/service/pair.service';
import { TokenService } from '../token/token.service';
import { SolanaService } from '../solana/solana.service';
import { sum } from 'bignumber.js';
import { TopHoldersMetricsException } from 'src/common/exceptions/top-holders-metrics-exception';

@Injectable()
export class TokenBalanceService {
  private readonly logger = new Logger(TokenBalanceService.name);

  constructor(
    @InjectQueue(QueueName.TOKEN_BALANCE_CHANGE)
    private readonly tokenBalanceChangeQueue: Queue,
    private readonly questdbSenderService: QuestdbSenderService,
    private readonly tokenBalanceRepository: TokenBalanceRepository,
    private readonly pairService: PairService,
    private readonly tokenService: TokenService,
    private readonly solanaService: SolanaService,
  ) {}

  async handleTokenBalanceChange(parsedTransactions: ParsedTransactionData[]) {
    const uniqueMap = new Map<string, TokenBalanceChangeDto>();

    for (const parsedTransaction of parsedTransactions) {
      const { postTokenBalances, slot, signature, index, blockTime } =
        parsedTransaction;
      if (!postTokenBalances || postTokenBalances.length === 0) {
        continue;
      }

      postTokenBalances.forEach((tokenBalance) => {
        const { mint, owner, amount } = tokenBalance;
        if (!mint || !owner || mint.toString() === SOL_ADDRESS) return;

        const key = `${slot}-${mint.toString()}-${owner.toString()}`;
        const newChange: TokenBalanceChangeDto = {
          tokenAddress: mint.toString(),
          owner: owner.toString(),
          amount: Number(amount),
          signature,
          slot,
          index,
          blockTime,
        };

        const existing = uniqueMap.get(key);

        if (!existing || newChange.index > existing.index) {
          uniqueMap.set(key, newChange);
        }
      });
    }

    const tokenBalanceChanges = Array.from(uniqueMap.values());

    if (tokenBalanceChanges?.length) {
      this.logger.log(
        `Found token balance changes for slot [${tokenBalanceChanges[0].slot}], changes count [${tokenBalanceChanges.length}]`,
      );
    }

    await this.tokenBalanceChangeQueue.add(TOKEN_BALANCE_CHANGE_JOB, {
      tokenBalanceChanges,
    });
  }

  async createMultipleTokenBalanceChanges(
    tokenBalanceChanges: TokenBalanceChangeDto[],
  ): Promise<{
    success: TokenBalanceChangeDto[];
    failed: { tx: TokenBalanceChangeDto; reason: string }[];
  }> {
    const begin = performance.now();

    const results = await Promise.allSettled(
      tokenBalanceChanges.map(async (change) => {
        await this.questdbSenderService.queueTokenBalanceChange(change);
        return change;
      }),
    );

    const success: TokenBalanceChangeDto[] = [];
    const failed: { tx: TokenBalanceChangeDto; reason: string }[] = [];

    results.forEach((result, i) => {
      const tx = tokenBalanceChanges[i];
      if (result.status === 'fulfilled') {
        success.push(result.value);
      } else {
        failed.push({
          tx,
          reason: result.reason?.message || 'Unknown error',
        });
      }
    });

    await this.questdbSenderService.flush();

    this.logger.log(
      `[INFO] Stored token balances: ${success.length} success, ${failed.length} failed, elapsed ${(performance.now() - begin).toFixed()}ms`,
    );

    if (failed.length > 0) {
      this.logger.warn(
        failed
          .map((f) => `${f.tx.tokenAddress}-${f.tx.owner}: ${f.reason}`)
          .join(', '),
      );
    }

    return { success, failed };
  }

  async getTopHolders(pairAddress: string) {
    if (!pairAddress) {
      throw new TokenMetricsException(
        pairAddress,
        'Missing pairAddress parameter',
      );
    }

    const pair = await this.pairService.getPairByPairAddress(pairAddress);
    if (!pair) {
      throw new PairNotFoundException(pairAddress);
    }

    const token = await this.tokenService.getTokenByAddress(pair.baseAddress);
    if (!token) {
      throw new TokenNotFoundException(pair.baseAddress);
    }

    const topHolders = await this.tokenBalanceRepository.findTopHolders(
      pair.baseAddress,
    );
  }

  async getTopHoldersTokenAmount(pairAddress: string) {
    const query =
      await this.tokenBalanceRepository.findTopHoldersTokenAmount(pairAddress);
    const array = query.map((item) => {
      return BigNumber(item);
    });
    return BigNumber.sum(...array).toNumber();
  }

  async getPercentageTop10Holders(pairAddress: string): Promise<number> {
    const pair = await this.tokenBalanceRepository.findTopHolders(pairAddress);
    if (!pair) {
      throw new PairNotFoundException(pairAddress);
    }
    const top10Holders =
      await this.tokenBalanceRepository.findTopHolders(pairAddress);
    if (!top10Holders) {
      throw new PairNotFoundException(pairAddress);
    }

    const amount: number = await this.getTopHoldersTokenAmount(pairAddress);
    const totalSupply = await this.solanaService.getTokenSupply(pairAddress);

    if (!totalSupply?.amount) {
      this.logger.error(
        `Total supply is not available for pairAddress ${pairAddress}`,
      );
      throw new TopHoldersMetricsException(
        pairAddress,
        'Missing topHolding metric',
      );
    }

    if (amount === undefined || amount === null) {
      this.logger.error(
        `Top holder token amount is not available for pairAddress ${pairAddress}`,
      );
      throw new TopHolderAmountUnavailableException(pairAddress);
    }

    if (Number(totalSupply.amount) === 0) {
    }
    return sum(BigNumber(amount))
      .div(totalSupply.amount)
      .multipliedBy(100)
      .toNumber();
  }
}
