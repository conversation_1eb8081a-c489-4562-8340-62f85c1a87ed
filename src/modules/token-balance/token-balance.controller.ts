import { Controller, Get, Query } from '@nestjs/common';
import { GetTopHoldersDto } from './dto/get-top-holders-request.dto';
import { TopHoldersData } from './interfaces/top-holders-data.interface';
import { TokenBalanceService } from './token-balance.service';

@Controller('token-balance')
export class TokenBalanceController {
  constructor(private readonly tokenBalanceService: TokenBalanceService) {}

  @Get('top-holders')
  async getTopHolders(
    @Query() query: GetTopHoldersDto,
  ): Promise<TopHoldersData[]> {
    return await this.tokenBalanceService.getTopHolders(query);
  }
}
