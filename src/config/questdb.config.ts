import { registerAs } from '@nestjs/config';
import {
  IsEnum,
  IsInt,
  IsOptional,
  IsString,
  IsBooleanString,
} from 'class-validator';
import validateConfig from 'src/common/utils/validate-config';

enum QuestDbSenderMode {
  HTTP = 'http',
  TCP = 'tcp',
}

class QuestDbEnvironmentVariablesValidator {
  @IsString()
  @IsOptional()
  QUESTDB_PG_HOST: string;

  @IsInt()
  @IsOptional()
  QUESTDB_PG_PORT: number;

  @IsString()
  @IsOptional()
  QUESTDB_PG_DATABASE: string;

  @IsString()
  @IsOptional()
  QUESTDB_PG_USER: string;

  @IsString()
  @IsOptional()
  QUESTDB_PG_PASSWORD: string;

  @IsBooleanString()
  @IsOptional()
  QUESTDB_RUN_MIGRATIONS: string;

  @IsEnum(QuestDbSenderMode)
  @IsOptional()
  QUESTDB_SENDER_MODE: QuestDbSenderMode;

  @IsString()
  @IsOptional()
  QUESTDB_SENDER_HOST: string;

  @IsInt()
  @IsOptional()
  QUESTDB_SENDER_PORT: number;
}

export interface QuestDbPgConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
  maxConnections: number;
  runMigrations: boolean;
}

export interface QuestDbSenderConfig {
  mode: QuestDbSenderMode;
  host: string;
  port: number;
}

validateConfig(process.env, QuestDbEnvironmentVariablesValidator);

export const questdbPgConfig = registerAs<QuestDbPgConfig>('questdbPg', () => ({
  host: process.env.QUESTDB_PG_HOST ?? 'localhost',
  port: parseInt(process.env.QUESTDB_PG_PORT ?? '8812', 10),
  database: process.env.QUESTDB_PG_DATABASE ?? 'qdb',
  user: process.env.QUESTDB_PG_USER ?? 'admin',
  password: process.env.QUESTDB_PG_PASSWORD ?? 'quest',
  maxConnections: parseInt(process.env.QUESTDB_PG_MAX_CONNECTIONS ?? '10', 10),
  runMigrations: process.env.QUESTDB_RUN_MIGRATIONS === 'true',
}));

export const questdbSenderConfig = registerAs<QuestDbSenderConfig>(
  'questdbSender',
  () => ({
    mode:
      (process.env.QUESTDB_SENDER_MODE as QuestDbSenderMode) ??
      QuestDbSenderMode.HTTP,
    host: process.env.QUESTDB_SENDER_HOST ?? 'localhost',
    port: parseInt(process.env.QUESTDB_SENDER_PORT ?? '9000', 10),
  }),
);
