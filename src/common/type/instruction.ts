export type Instruction = {
  programId: string;
  accounts: string[];
  data: string;
};

export type InnerInstruction = {
  index: number;
  instructions: Instruction[];
};

export type RawInstruction = {
  programIdIndex: number;
  accounts: {
    type: 'Buffer';
    data: number[];
  };
  data: {
    type: 'Buffer';
    data: number[];
  };
};

export type RawInnerInstruction = {
  index: number;
  instructions: RawInstruction[];
};
