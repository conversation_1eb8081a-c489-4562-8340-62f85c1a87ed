import { public<PERSON><PERSON>, u64 } from '@solana/buffer-layout-utils';
import { PublicKey } from '@solana/web3.js';
import { struct } from '@solana/buffer-layout';

export const PUMP_SWAP_POOL_SELL_LAYOUT = struct<PumpSwapSellLayout>([
  u64('timestamp'),
  u64('base_amount_in'),
  u64('min_quote_amount_out'),
  u64('user_base_token_reserves'),
  u64('user_quote_token_reserves'),
  u64('pool_base_token_reserves'),
  u64('pool_quote_token_reserves'),
  u64('quote_amount_out'),
  u64('lp_fee_basis_points'),
  u64('lp_fee'),
  u64('protocol_fee_basis_points'),
  u64('protocol_fee'),
  u64('quote_amount_out_without_lp_fee'),
  u64('user_quote_amount_out'),
  public<PERSON><PERSON>('pool'),
  public<PERSON>ey('user'),
  public<PERSON><PERSON>('user_base_token_account'),
  public<PERSON>ey('user_quote_token_account'),
  public<PERSON>ey('protocol_fee_recipient'),
  public<PERSON><PERSON>('protocol_fee_recipient_token_account'),
]);

export interface PumpSwapSellLayout {
  _reserved: Buffer;
  timestamp: bigint;
  base_amount_in: bigint;
  min_quote_amount_out: bigint;
  user_base_token_reserves: bigint;
  user_quote_token_reserves: bigint;
  pool_base_token_reserves: bigint;
  pool_quote_token_reserves: bigint;
  quote_amount_out: bigint;
  lp_fee_basis_points: bigint;
  lp_fee: bigint;
  protocol_fee_basis_points: bigint;
  protocol_fee: bigint;
  quote_amount_out_without_lp_fee: bigint;
  user_quote_amount_out: bigint;
  pool: PublicKey;
  user: PublicKey;
  user_base_token_account: PublicKey;
  user_quote_token_account: PublicKey;
  protocol_fee_recipient: PublicKey;
  protocol_fee_recipient_token_account: PublicKey;
}

export const PUMP_SWAP_POOL_BUY_LAYOUT = struct<PumpSwapBuyLayout>([
  u64('timestamp'),
  u64('base_amount_out'),
  u64('max_quote_amount_in'),
  u64('user_base_token_reserves'),
  u64('user_quote_token_reserves'),
  u64('pool_base_token_reserves'),
  u64('pool_quote_token_reserves'),
  u64('quote_amount_in'),
  u64('lp_fee_basis_points'),
  u64('lp_fee'),
  u64('protocol_fee_basis_points'),
  u64('protocol_fee'),
  u64('quote_amount_in_with_lp_fee'),
  u64('user_quote_amount_in'),
  publicKey('pool'),
  publicKey('user'),
  publicKey('user_base_token_account'),
  publicKey('user_quote_token_account'),
  publicKey('protocol_fee_recipient'),
  publicKey('protocol_fee_recipient_token_account'),
]);

export interface PumpSwapBuyLayout {
  _reserved: Buffer;
  timestamp: bigint;
  base_amount_out: bigint;
  max_quote_amount_in: bigint;
  user_base_token_reserves: bigint;
  user_quote_token_reserves: bigint;
  pool_base_token_reserves: bigint;
  pool_quote_token_reserves: bigint;
  quote_amount_in: bigint;
  lp_fee_basis_points: bigint;
  lp_fee: bigint;
  protocol_fee_basis_points: bigint;
  protocol_fee: bigint;
  quote_amount_in_with_lp_fee: bigint;
  user_quote_amount_in: bigint;
  pool: PublicKey;
  user: PublicKey;
  user_base_token_account: PublicKey;
  user_quote_token_account: PublicKey;
  protocol_fee_recipient: PublicKey;
  protocol_fee_recipient_token_account: PublicKey;
}
