import { HttpStatus } from '@nestjs/common';
import { AppError } from '../errors/app-error';
import { POOL_DISCOVERY_SERVICE_ID } from '../errors/error.service';
import { POOL_DISCOVERY_CLIENT_ERROR_ID } from '../constant/error-codes';

/**
 * Exception thrown when top holders metrics cannot be retrieved
 */
export class TopHoldersMetricsException extends AppError {
  constructor(tokenAddress: string, reason?: string) {
    super(
      HttpStatus.BAD_REQUEST,
      POOL_DISCOVERY_SERVICE_ID,
      POOL_DISCOVERY_CLIENT_ERROR_ID.TOP_HOLDERS_METRICS_ERROR,
      `Unable to fetch token metrics for address: ${tokenAddress}${reason ? ` - ${reason}` : ''}`,
      { tokenAddress, reason },
    );
  }
}
