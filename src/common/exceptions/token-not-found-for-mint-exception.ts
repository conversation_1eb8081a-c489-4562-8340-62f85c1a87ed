import { HttpStatus } from '@nestjs/common';
import { AppError } from '../errors/app-error';
import { POOL_DISCOVERY_SERVICE_ID } from '../errors/error.service';
import { POOL_DISCOVERY_CLIENT_ERROR_ID } from '../constant/error-codes';

/**
 * Exception thrown when no token accounts found for the specified mint
 */
export class TokenNotFoundForMintException extends AppError {
  constructor(tokenAddress: string) {
    super(
      HttpStatus.BAD_GATEWAY,
      POOL_DISCOVERY_SERVICE_ID,
      POOL_DISCOVERY_CLIENT_ERROR_ID.TOKEN_NOT_FOUND,
      `No token accounts found for the specified mint: ${tokenAddress}`,
      { tokenAddress },
    );
  }
}
