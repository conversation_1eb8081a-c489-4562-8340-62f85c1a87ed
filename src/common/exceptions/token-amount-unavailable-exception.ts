import { HttpStatus } from '@nestjs/common';
import { AppError } from '../errors/app-error';
import { POOL_DISCOVERY_SERVICE_ID } from '../errors/error.service';
import { POOL_DISCOVERY_CLIENT_ERROR_ID } from '../constant/error-codes';

/**
 * Exception thrown when token amount is not available
 */
export class TokenAmountUnavailableException extends AppError {
  constructor(tokenAddress: string) {
    super(
      HttpStatus.BAD_REQUEST,
      POOL_DISCOVERY_SERVICE_ID,
      POOL_DISCOVERY_CLIENT_ERROR_ID.TOKEN_AMOUNT_UNAVAILABLE,
      `Token amount is not available for tokenAddress ${tokenAddress}`,
      { tokenAddress },
    );
  }
}
