export enum PumpFunQueue {
  NEW_MULTIPLE_TRADE_TRANSACTION = 'pumpfun-multiple-new-trade',
  NEW_PAIR = 'pumpfun-new-pair',
  NEW_TOKEN = 'pumpfun-new-token',
  UPDATE_MULTIPLE_PAIR = 'pumpfun-update-multiple-pair',
}

export enum PumpSwapQueue {
  NEW_PUMPSWAP_PAIR = 'pumpswap-new-pair',
  NEW_TRADE_TRANSACTION = 'pumpswap-new-trade',
  NEW_MULTIPLE_TRADE_TRANSACTION = 'pumpswap-multiple-new-trade',
  NEW_TOKEN = 'pumpswap-new-token',
  NEW_PAIR_DETAIL = 'pumpswap-new-pair-detail',
  UPDATE_MULTIPLE_PAIR = 'pumpswap-update-multiple-pair',
}

export enum FlowName {
  NEW_PUMPSWAP_PAIR = 'pumpswap-new-pair',
  NEW_POOL_DETAIL = 'pumpswap-new-pair-detail',
}

export enum QueueName {
  TOKEN_BALANCE_CHANGE = 'token-balance-change',
}
