/**
 * Service identifiers as per error convention
 */
export const SERVICE_ID = {
  POOL_DISCOVERY: '06',
};

/**
 * Common Client Error codes (4xx)
 * All common client error IDs start with a 0 prefix
 */
export const COMMON_CLIENT_ERROR_ID = {
  BAD_REQUEST: '0001',
  UNAUTHORIZED: '0002',
  PAYMENT_REQUIRED: '0003',
  FORBIDDEN: '0004',
  NOT_FOUND: '0005',
  METHOD_NOT_ALLOWED: '0006',
  NOT_ACCEPTABLE: '0007',
  REQUEST_TIMEOUT: '0008',
  CONFLICT: '0009',
  GONE: '0010',
  UNPROCESSABLE_ENTITY: '0020',
  TOO_MANY_REQUESTS: '0026',
};

/**
 * Common Server Error codes (5xx)
 * All server error IDs start with a 5 prefix
 */
export const COMMON_SERVER_ERROR_ID = {
  INTERNAL_SERVER_ERROR: '5001',
  NOT_IMPLEMENTED: '5002',
  BAD_GATEWAY: '5003',
  SERVICE_UNAVAILABLE: '5004',
  GATEWAY_TIMEOUT: '5005',
  HTTP_VERSION_NOT_SUPPORTED: '5006',
};

/**
 * Custom Error Categories
 * All custom error categories start with a 9 prefix
 */
export const COMMON_CUSTOM_ERROR_ID = {
  VALIDATION: '9001',
  DATABASE: '9002',
  EXTERNAL: '9003',
  BUSINESS: '9004',
  SECURITY: '9005',
};

/**
 * Pool Discovery Service Error Codes (Client errors)
 */
export const POOL_DISCOVERY_CLIENT_ERROR_ID = {
  // 400 Bad Request errors
  INVALID_TOKEN_ADDRESS: '0001',
  INVALID_PAIR_ADDRESS: '0002',
  INVALID_REQUEST_PARAMS: '0003',
  INVALID_PAGINATION_PARAMS: '0004',
  INVALID_SORT_DIRECTION: '0005',
  INVALID_DATE_RANGE: '0006',
  INVALID_TIME_INTERVAL: '0007',
  TOKEN_METRICS_ERROR: '0008',
  TOP_HOLDERS_METRICS_ERROR: '0010',

  // 404 Not Found errors
  TOKEN_NOT_FOUND: '0100',
  PAIR_NOT_FOUND: '0101',
  TRANSACTION_NOT_FOUND: '0102',
  DEX_NOT_FOUND: '0103',
  DEX_LABEL_NOT_FOUND: '0104',
  TOKEN_AMOUNT_UNAVAILABLE: '0105',
  TOP_HOLDER_AMOUNT_UNAVAILABLE: '0107',

  // 409 Conflict errors
  TOKEN_ALREADY_EXISTS: '0200',
  PAIR_ALREADY_EXISTS: '0201',
};

/**
 * Pool Discovery Service Error Codes (Server errors)
 */
export const POOL_DISCOVERY_SERVER_ERROR_ID = {
  // 500 Internal Server Error - Database related
  FAILED_TO_SAVE_TOKEN: '5001',
  FAILED_TO_SAVE_PAIR: '5002',
  FAILED_TO_SAVE_TRANSACTION: '5003',
  FAILED_TO_UPDATE_PAIR_DETAILS: '5004',
  DATABASE_CONNECTION_ERROR: '5005',
  DATABASE_QUERY_ERROR: '5006',

  // 500 Internal Server Error - Queue related
  FAILED_TO_QUEUE_TOKEN: '5100',
  FAILED_TO_QUEUE_PAIR: '5101',
  FAILED_TO_QUEUE_TRANSACTION: '5102',
  FAILED_TO_PROCESS_QUEUE_JOB: '5103',

  // 500 Internal Server Error - External service related
  FAILED_TO_FETCH_BLOCKCHAIN_DATA: '5200',
  FAILED_TO_PROCESS_BLOCKCHAIN_EVENT: '5201',
  SOLANA_RPC_ERROR: '5202',

  // 503 Service Unavailable
  SERVICE_UNAVAILABLE: '5300',
};
