export const PUMPFUN_PROGRAM_ID = '6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P';

export const PUMPFUN_TRADE_LOG_MESSAGE = [
  'Program log: Instruction: Buy',
  'Program log: Instruction: Sell',
];

export const PUMPFUN_CREATE_LOG_MESSAGE = 'Program log: Instruction: Create';

export enum PumpfunEvent {
  TRADE = 'TradeEvent',
}

export const PUMPFUN_TO_RAYDIUM_MIGRATION_AMOUNT = 793_100_000_000_000;

export const PUMPFUN_AND_SOL_DIFFERENTIAL = 1e-3;

export const PUMPFUN_MIGRATION_SIGNER =
  '39azUYFWPz3VHgKCf3VChUwbpURdCHRxjWVowf5jUJjg';
