export enum PairDetailErrorMessage {
  ERROR_QUEUE_UPDATE_PAIR_DETAIL = 'Error queue update pair detail',
}

export enum PairErrorMessage {
  ERROR_QUEUE_NEW_PAIR = 'Error queue new pair',
}

export enum TokenErrorMessage {
  ERROR_QUEUE_NEW_TOKEN = 'Error queue new token',
}

export enum TokenBalanceErrorMessage {
  ERROR_QUEUE_NEW_TOKEN_BALANCE = 'Error queue new token balance',
}

export enum SolanaServiceErrorMessage {
  ERROR_GET_TOKEN_AMOUNT_BY_OWNER = 'Error get token amount by owner',
}
