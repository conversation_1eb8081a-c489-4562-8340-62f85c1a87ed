// In this file you can configure migrate-mongo
import dotenv from 'dotenv';
dotenv.config();

export = {
  mongodb: {
    url:
      process.env.MONGO_USERNAME && process.env.MONGO_PASSWORD
        ? `mongodb://${process.env.MONGO_USERNAME}:${process.env.MONGO_PASSWORD}@${process.env.MONGO_URI?.replace('mongodb://', '') || 'localhost:27017'}`
        : process.env.MONGO_URI || 'mongodb://localhost:27017',

    databaseName: process.env.MONGO_DB_NAME || 'dexmarket',

    options: {
      //   connectTimeoutMS: 3600000, // increase connection timeout to 1 hour
      //   socketTimeoutMS: 3600000, // increase socket timeout to 1 hour
    },
  },

  // The migrations dir, can be a relative or absolute path. Only edit this when really necessary.
  migrationsDir: './src/database/mongo-migrations',

  // The mongodb collection where the applied changes are stored. Only edit this when really necessary.
  changelogCollectionName: 'migrations',

  // The mongodb collection where the lock will be created.
  lockCollectionName: 'migrations_lock',

  // The value in seconds for the TTL index that will be used for the lock. Value of 0 will disable the feature.
  lockTtl: 0,

  // The file extension to create migrations and search for in migration dir
  migrationFileExtension: '.ts',

  // Enable the algorithm to create a checksum of the file contents and use that in the comparison to determine
  // if the file should be run.  Requires that scripts are coded to be run multiple times.
  useFileHash: false,

  // Don't change this, unless you know what you're doing
  moduleSystem: 'commonjs',
};
