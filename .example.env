APP_PORT=3001
#  error: 0,
#  warn: 1,
#  info: 2,
#  http: 3,
#  verbose: 4,
#  debug: 5,
#  silly: 6
LOG_LEVEL=info
WS_AUTH_TOKEN=

SOLANA_RPC_URL=
SOLANA_WSRPC_URL=
SOLANA_GRPC_URL=
SOLANA_GRPC_X_TOKEN=
SOLANA_COMMITMENT=confirmed
SOLANA_DISABLE_RETRY_ON_RATE_LIMIT=true
SOLANA_CONFIRM_TRX_TIMEOUT=60_000

REDIS_HOST=
REDIS_PORT=
REDIS_PASSWORD=
REDIS_DB=
#sentinel, default
REDIS_MODE=
REDIS_SENTINEL_NODES=
REDIS_SENTINEL_MASTER_NAME=
REDIS_SENTINEL_PASSWORD=

REDIS_BULL_HOST=
REDIS_BULL_PORT=
REDIS_BULL_PASSWORD=
REDIS_BULL_DB=
#sentinel, default
REDIS_BULL_MODE=
REDIS_BULL_SENTINEL_NODES=
REDIS_BULL_SENTINEL_MASTER_NAME=
REDIS_BULL_SENTINEL_PASSWORD=

KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=pumpfun-subservice

KAFKA_GROUP_ID=
KAFKA_IS_SASL_AUTH=
KAFKA_USERNAME=
KAFKA_PASSWORD=
KAFKA_SASL_MECHANISM=

# PostgreSQL wire protocol
QUESTDB_PG_HOST=localhost
QUESTDB_PG_PORT=8812
QUESTDB_PG_DATABASE=qdb
QUESTDB_PG_USER=admin
QUESTDB_PG_PASSWORD=quest
QUESTDB_RUN_MIGRATIONS=true
QUESTDB_PG_MAX_CONNECTIONS=10

# REST API + Web UI
QUESTDB_SENDER_MODE=http
QUESTDB_SENDER_HOST=localhost
QUESTDB_SENDER_PORT=9000

# mongo
MONGO_URI=mongodb://localhost:27017
MONGO_DB_NAME=dexmarket
MONGO_USERNAME=dexuser
MONGO_PASSWORD=supersecurepassword
MONGO_RUN_MIGRATIONS=true

GRPC_URL=localhost:50051