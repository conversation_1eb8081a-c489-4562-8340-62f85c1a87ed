#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

commit_msg_file=$1
commit_msg=$(cat "$commit_msg_file")

# Define the expected format pattern
# Format: <type>(<ticket-id>): <short description>
valid_types="feat|fix|revert|refactor|chore|docs|test|style"
pattern="^($valid_types)(\(.+\)): .+"

if ! echo "$commit_msg" | grep -E "$pattern" > /dev/null; then
  echo "❌ Invalid commit message format! Please use the format: <type>(<ticket-id>): <short description>"
  echo "Valid types: feat, fix, revert, refactor, chore, docs, test, style"
  echo "Example: feat(SCRUM-123): add login API integration"
  exit 1
fi

echo "✅ Commit message format is valid!"
