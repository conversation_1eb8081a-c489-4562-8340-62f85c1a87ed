#
# Development
#
FROM node:22-alpine as dev
# add the missing shared libraries from alpine base image
RUN apk add --no-cache libc6-compat

WORKDIR /app
# Set to dev environment
ENV NODE_ENV development

# Create non-root user for Docker
# Version node:18 already has group node and user node
#RUN addgroup --system --gid 1001 node
#RUN adduser --system --uid 1001 node

# Copy source code into app folder
COPY --chown=node:node . .

# Install dependencies
RUN yarn --frozen-lockfile

# In development mode, nestjs need permissions to modify dist folder
RUN mkdir /app/dist
RUN chown -R node:node /app
# Set Docker as a non-root user
USER node
# Run app on development mode
CMD ["yarn", "run", "start:dev"]

#
# Production build
#
FROM node:22-alpine as build
# add the missing shared libraries from alpine base image
RUN apk add --no-cache libc6-compat

WORKDIR /app
# Set to production environment
ENV NODE_ENV production

# In order to run `yarn build` we need access to the Nest CLI.
# Nest CLI is a dev dependency.
COPY --chown=node:node --from=dev /app/node_modules ./node_modules
# Copy source code
COPY --chown=node:node . .

RUN yarn build

# Install only the production dependencies and clean cache to optimize image size.
# Notice: We dont need to use the --production flag when installing dependencies
# because we already set NODE_ENV to production
RUN yarn --frozen-lockfile && yarn cache clean

# Set Docker as a non-root user
USER node

#
# Production
#
FROM node:22-alpine as production

RUN apk --no-cache add curl

WORKDIR /app
ENV NODE_ENV production

# Copy only the necessary files
COPY --chown=node:node --from=build /app/dist dist
COPY --chown=node:node --from=build /app/node_modules node_modules

RUN mkdir /app/logs
RUN chown -R node:node /app/logs

# Set Docker as non-root user
USER node

CMD ["node", "dist/main.js"]
