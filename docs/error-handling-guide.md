// filepath: /Users/<USER>/project/dexmarket/dexmarket-pool-discovery/docs/error-handling-guide.md
# Error Handling Guide

This guide provides instructions on how to handle errors in our microservices, specifically for the Pool Discovery service.

## Using the Error System

### 1. Using Existing Exception Classes

We have a variety of pre-defined exception classes that follow our error convention. Use these whenever possible:

```typescript
import { TokenNotFoundException } from '../common/exceptions';

// In your service or controller
if (!token) {
  throw new TokenNotFoundException(tokenAddress);
}
```

### 2. Creating Service-Specific Errors

For service-specific errors that don't have a pre-defined exception class:

```typescript
import { HttpStatus } from '@nestjs/common';
import { AppError } from '../common/errors/app-error';
import { POOL_DISCOVERY_SERVICE_ID } from '../common/errors/error.service';
import { POOL_DISCOVERY_SERVER_ERROR_ID } from '../common/constant/error-codes';

// In your service
throw new AppError(
  HttpStatus.INTERNAL_SERVER_ERROR,
  POOL_DISCOVERY_SERVICE_ID,
  POOL_DISCOVERY_SERVER_ERROR_ID.FAILED_TO_SAVE_TOKEN,
  'Failed to save token',
  { tokenAddress }
);
```

### 3. Using the ErrorService

The `ErrorService` provides helper methods for creating common errors:

```typescript
import { ErrorService } from '../common/errors/error.service';

// In your service constructor
constructor(private readonly errorService: ErrorService) {}

// In your service method
throw this.errorService.createValidationError('Invalid request parameters', { param: value });
```

## Error Categories

### Common Errors

- Use the `COMMON_SERVICE_ID` (00) for errors that might happen across services
- These include validation, database, external service, and security errors

### Service-Specific Errors

- Use the specific service ID (e.g., `POOL_DISCOVERY_SERVICE_ID` for Pool Discovery)
- Service-specific error IDs are defined in `error-codes.ts`

## Adding New Error Codes

If you need to add new error codes:

1. Update the error-codes.ts file with your new error code
2. Create a new exception class if it's a common case
3. Document the new error code in the error catalog

## HTTP Exception Filter

The HTTP exception filter automatically transforms NestJS exceptions into our standardized error format. Custom exceptions extending `AppError` will maintain their error details.

## Best Practices

1. Always include meaningful error messages
2. Add relevant details in the details object
3. Use the appropriate HTTP status code
4. Group similar errors under the same error ID prefix
5. For validation errors, include the validation issues in the details
6. For database errors, include non-sensitive database information
7. Always log errors with their trace ID

## Sample Usage

```typescript
// Validation error
throw new ValidationException('Invalid request parameters', {
  issues: [
    { field: 'email', issue: 'Must be a valid email' },
    { field: 'password', issue: 'Must be at least 8 characters' }
  ]
});

// Not found error
throw new TokenNotFoundException(tokenAddress);

// Database error
throw new DatabaseQueryException('Failed to execute query', {
  operation: 'findToken',
  params: { address: tokenAddress }
});

// External service error
throw new ExternalServiceException('Solana RPC', 'Connection error', {
  endpoint: rpcUrl,
  attemptCount: 3
});
```
