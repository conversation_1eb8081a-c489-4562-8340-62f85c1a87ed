# 1. Ticket-Based Branch Naming

Use the following format:
```
<type>/<ticket-id>-<short-description>
```
Examples:

- `feat/SCRUM-123-add-login`

 
## Allowed Types:

- **feat** – New feature
- **fix** – Bug fix
- **revert** – Revert code
- **refactor** – Code refactoring
- **chore** – Maintenance (non-feature, non-bug)
- **docs** – Documentation updates
- **test** – Adding or updating tests
- **style** – Code style changes (e.g., formatting, missing semicolons)

#  2. Commit Message Format
```
<type>(<ticket-id>): <short description>
<optional body>
```
Example:
```
feat(SCRUM-123): add login API integration
.  Integrated with backend API
.  Handled loading and error states
```
