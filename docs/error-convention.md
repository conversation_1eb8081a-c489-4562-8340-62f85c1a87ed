# Error Handling Convention for Microservices

## Introduction

This document outlines the standard approach for error handling across all services in our microservices architecture. The goal is to create a uniform error system that:

- Provides clear and detailed error information
- Enables effective monitoring and debugging
- Maintains consistency in all API responses
- Supports multiple protocols (HTTP, gRPC)
- Makes error identification and resolution easier

## Error Structure

### Error Code Format

Each error has a unique code in the format: `XXXYYZZZZ`

Where:
- `XXX`: HTTP status code (3 digits)
- `YY`: Service identifier (2 digits)
- `ZZZZ`: Error identifier within the service (4 digits)

For example:
- `4000100001`: 400 (Bad Request) + 01 (User Service) + 0001 (Invalid Email)
- `4000100002`: 400 (Bad Request) + 01 (User Service) + 0002 (Invalid Phone)
- `5000200001`: 500 (Internal Server Error) + 02 (Payment Service) + 0001 (Data race)
- `5000200003`: 500 (Internal Server Error) + 02 (Payment Service) + 0003 (Database Connection Failed)
- `404000005`: 404 (Not Found) + 00 (Common/Shared) + 0005 (Not Found)

### Service Identifiers

| Service ID | Service Name           |
|------------|------------------------|
| 00         | Common/Shared Errors   |
| 01         | User Service           |
| 02         | Payment Service        |
| 03         | Product Service        |
| 04         | Order Service          |
| 05         | Notification Service   |
| 06         | Pool Discovery Service |
| ...        | ...                    |

### Common Error Categories

The system includes a special service ID `00` for common errors across services with standardized error IDs:

#### Client Error IDs (4xx)

All client error IDs start with a `0` prefix:

| Error ID | HTTP Status | Description |
|----------|-------------|-------------|
| 0001     | 400         | Bad Request |
| 0002     | 401         | Unauthorized |
| 0003     | 402         | Payment Required |
| 0004     | 403         | Forbidden |
| 0005     | 404         | Not Found |
| 0006     | 405         | Method Not Allowed |
| 0007     | 406         | Not Acceptable |
| 0008     | 408         | Request Timeout |
| 0009     | 409         | Conflict |
| 0010     | 410         | Gone |
| ...      | ...         | ... |
| 0020     | 422         | Unprocessable Entity |
| 0026     | 429         | Too Many Requests |

#### Server Error IDs (5xx)

All server error IDs start with a `5` prefix:

| Error ID | HTTP Status | Description |
|----------|-------------|-------------|
| 5001     | 500         | Internal Server Error |
| 5002     | 501         | Not Implemented |
| 5003     | 502         | Bad Gateway |
| 5004     | 503         | Service Unavailable |
| 5005     | 504         | Gateway Timeout |
| 5006     | 505         | HTTP Version Not Supported |
| ...      | ...         | ... |

#### Custom Error Categories

All custom error categories start with a `9` prefix:

| Error ID | Category | Description |
|----------|----------|-------------|
| 9001     | Validation | Validation errors (400) |
| 9002     | Database | Database related errors (500) |
| 9003     | External | External service errors (500) |
| 9004     | Business | Business logic errors (400) |
| 9005     | Security | Security related errors (401/403) |

## Standard Error Response

All API responses will follow this structure:

```json
{
  "success": false,
  "error": {
    "code": "XXXYYZZZZ",
    "message": "Human-readable error message",
    "details": {
      "field": "Additional error context",
      ...
    },
    "trace_id": "unique-trace-identifier"
  },
  "data": null
}
```
