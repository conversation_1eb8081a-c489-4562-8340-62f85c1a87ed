# Architecture

---

## Table of Contents

- [Overview](#overview)
- [Hexagonal Architecture](#hexagonal-architecture)
- [Project Structure](#project-structure)
- [Module Organization](#module-organization)
- [Data Flow](#data-flow)
- [Persistence Layer](#persistence-layer)
- [Infrastructure Services](#infrastructure-services)
- [Event Processing](#event-processing)
- [Configuration](#configuration)
- [<PERSON>rro<PERSON>](#error-handling)

---

## Overview

The Dexmarket Pool Discovery service is a NestJS-based application designed to discover, track, and analyze DEX pools on the Solana blockchain. It's built using Hexagonal Architecture principles to ensure separation of business logic from infrastructure concerns, making the codebase more maintainable and testable.

The application integrates with multiple external services and data sources:
- Solana blockchain for on-chain data
- MongoDB for document storage
- QuestDB for time-series data
- Kafka for event streaming
- Redis for caching and message brokering
- BullMQ for job queue management

## Hexagonal Architecture

This project follows the Hexagonal Architecture pattern (also known as Ports and Adapters) to separate domain logic from external dependencies. The key principles applied are:

![Hexagonal Architecture Diagram](https://github.com/brocoders/nestjs-boilerplate/assets/6001723/6a6a763e-d1c9-43cc-910a-617cda3a71db)

- **Domain Layer**: Contains business entities and logic without dependencies on external systems
- **Application Layer**: Contains use cases, application services, and orchestrates domain objects
- **Infrastructure Layer**: Contains adapters for external resources (databases, blockchain, message queues, etc.)
- **Ports**: Interfaces defined in the domain/application layer that are implemented by adapters

This architecture allows us to:
1. Swap infrastructure components without changing business logic
2. Test business logic in isolation from infrastructure
3. Evolve domain logic independently of external dependencies

## Project Structure

The project is organized as a NestJS application with the following high-level structure:

```
src/
├── app.module.ts            # Main application module
├── main.ts                  # Application entry point
├── config/                  # Configuration files
├── common/                  # Shared utilities, constants, filters, etc.
├── database/                # Database connection and migration
└── modules/                 # Feature modules
    ├── token/               # Token-related functionality
    ├── pair/                # Trading pair functionality
    ├── swap-transaction/    # Swap transaction tracking
    ├── kafka/               # Kafka integration
    ├── questdb/             # QuestDB time-series database integration
    ├── redis/               # Redis caching and messaging
    ├── queue/               # Job queue management
    └── solana/              # Solana blockchain integration
```

## Module Organization

Each feature module follows a similar structure that reflects the hexagonal architecture:

```
module/
├── domain/                  # Domain entities and interfaces
├── dto/                     # Data Transfer Objects for API
├── infrastructure/          # Implementation of infrastructure adapters
│   └── persistence/         # Database persistence implementations
│       ├── document/        # Document DB (MongoDB) implementations
│       │   ├── entities/    # MongoDB schema definitions
│       │   ├── mappers/     # Mappers between domain and DB entities
│       │   └── repositories/# MongoDB repository implementations
│       └── time-series/     # Time-series DB (QuestDB) implementations
│           ├── entities/    # QuestDB table definitions
│           ├── mappers/     # Mappers between domain and DB entities
│           └── repositories/# QuestDB repository implementations
├── processor/               # Queue job processors
├── controller.ts            # API controllers (when applicable)
├── service.ts               # Application services
└── module.ts                # NestJS module definition
```

## Data Flow

The application processes data through several stages:

1. **Input Sources**:
   - Kafka streams of blockchain events
   - RESTful API requests
   - gRPC service calls
   - Scheduled jobs

2. **Processing**:
   - Events are received and validated
   - Data is processed by the appropriate domain services
   - Changes are persisted to databases
   - Events are emitted for async processing

3. **Storage**:
   - Document data stored in MongoDB (tokens, pairs information)
   - Time-series data stored in QuestDB (transactions, price history)
   - Cache data stored in Redis

4. **Output**:
   - API responses (REST/gRPC)
   - Events published to subscribers
   - Jobs enqueued for background processing

## Persistence Layer

The application uses multiple data storage technologies:

### MongoDB (Document Storage)

Used for storing token and pair information as documents. The integration is implemented using:
- NestJS Mongoose module
- Schema definitions under `*.schema.ts` files
- Repository pattern to abstract database access

### QuestDB (Time-Series Database)

Used for storing high-frequency time-series data like swap transactions and price changes. The integration includes:
- Custom QuestDB client integration
- Repository pattern implementations
- Query builders for efficient time-series queries

### Redis (Cache)

Used for caching frequently accessed data and as a backing store for BullMQ. The integration includes:
- Custom Redis client factory
- Repository pattern for Redis operations
- Service integrations for caching strategies

## Infrastructure Services

### Kafka Integration

The application consumes blockchain events via Kafka:
- Kafka consumer module with handlers for different event types
- Event-to-domain mapping for processing
- Producer capabilities for publishing events

### BullMQ Queue Processing

Background processing is handled using BullMQ:
- Queue configurations for different job types
- Job processors for asynchronous tasks
- Schedulers for recurring jobs

### Solana Blockchain Integration

Interaction with the Solana blockchain:
- Connection management and retry logic
- Transaction parsing and validation
- On-chain data retrieval and analysis
- Custom layout definitions for program account data

## Event Processing

The application uses an event-driven architecture pattern:
- EventEmitterModule for in-process events
- Kafka for cross-service events
- Event handlers organized by domain
- Event serialization and deserialization

## Configuration

Configuration is managed through:
- Environment variables loaded via dotenv
- Configuration modules for different components
- Validation at startup to ensure required configuration is present
- Service-specific configuration sections

## Error Handling

The application includes comprehensive error handling:
- Global exception filters for HTTP, WebSocket, and gRPC
- Custom domain exceptions with meaningful error codes
- Logging with appropriate context
- Graceful degradation when external services fail

---

*This documentation is intended to provide a high-level overview of the system architecture. Refer to specific module documentation for more detailed information.*
